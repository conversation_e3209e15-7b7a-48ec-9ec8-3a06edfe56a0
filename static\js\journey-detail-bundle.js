/* Journey Detail Bundle JS - Complete unified bundle for journey detail pages */

/* This bundle combines JavaScript needed for journey detail pages:
 * This eliminates conflicts and reduces HTTP requests
 *
 * Note: This bundle is loaded at the end of the body to ensure flash messages
 * and other base.html functionality is available when needed.
 */

/* ===== ENHANCED FORM VALIDATION ===== */

/**
 * Enhanced Form Validation Utility
 * Provides modern form validation with real-time feedback
 */
class EnhancedFormValidation {
  static initializeModernForm(form, options = {}) {
    if (!form) return;

    const defaults = {
      validateOnInput: true,
      validateOnBlur: true,
      showSuccessStates: true,
      customValidators: {},
    };

    const config = { ...defaults, ...options };

    // Add validation event listeners
    if (config.validateOnInput) {
      form.addEventListener("input", (e) => {
        this.validateField(e.target, config);
      });
    }

    if (config.validateOnBlur) {
      form.addEventListener(
        "blur",
        (e) => {
          this.validateField(e.target, config);
        },
        true
      );
    }

    form.addEventListener("submit", (e) => {
      if (!this.validateForm(form, config)) {
        e.preventDefault();
        e.stopPropagation();
      }
    });
  }

  static validateField(field, config) {
    if (!field.checkValidity) return true;

    const isValid = field.checkValidity();

    field.classList.remove("is-valid", "is-invalid");

    if (isValid && config.showSuccessStates && field.value.trim() !== "") {
      field.classList.add("is-valid");
    } else if (!isValid) {
      field.classList.add("is-invalid");
    }

    return isValid;
  }

  static validateForm(form, config) {
    let isValid = true;
    const fields = form.querySelectorAll("input, textarea, select");

    fields.forEach((field) => {
      if (!this.validateField(field, config)) {
        isValid = false;
      }
    });

    return isValid;
  }
}

/* ===== IMAGE PREVIEW ===== */

/**
 * Image Preview Utility
 * Provides image preview functionality for file inputs
 */
class ImagePreview {
  static initialize(inputSelector, containerSelector, options = {}) {
    const input = document.querySelector(inputSelector);
    const container = document.querySelector(containerSelector);

    if (!input || !container) return;

    const defaults = {
      maxFileSize: 5 * 1024 * 1024,
      allowedTypes: ["image/jpeg", "image/jpg", "image/png", "image/gif"],
      maxFiles: 1,
      showFileName: true,
      showFileSize: true,
      onImageLoad: null,
      onError: null,
    };

    const config = { ...defaults, ...options };

    input.addEventListener("change", (e) => {
      this.handleFileSelection(e.target, container, config);
    });

    window.imagePreviewInstance = {
      clearAll: () => this.clearPreviews(container),
    };
  }

  static handleFileSelection(input, container, config) {
    const files = Array.from(input.files);

    if (files.length > config.maxFiles) {
      this.showError(`Maximum ${config.maxFiles} files allowed`, config);
      return;
    }

    this.clearPreviews(container);
    container.style.display = "block";

    files.forEach((file, index) => {
      if (!this.validateFile(file, config)) return;

      this.createPreview(file, container, config, index);
    });
  }

  static validateFile(file, config) {
    if (!config.allowedTypes.includes(file.type)) {
      this.showError(`File type ${file.type} not allowed`, config);
      return false;
    }

    if (file.size > config.maxFileSize) {
      this.showError(
        `File size exceeds ${config.maxFileSize / (1024 * 1024)}MB limit`,
        config
      );
      return false;
    }

    return true;
  }

  static createPreview(file, container, config, index) {
    const reader = new FileReader();

    reader.onload = (e) => {
      const previewElement = this.createPreviewElement(
        file,
        e.target.result,
        config,
        index
      );
      const grid = container.querySelector("#previewGrid") || container;
      grid.appendChild(previewElement);

      if (config.onImageLoad) {
        config.onImageLoad(file, e.target.result);
      }
    };

    reader.readAsDataURL(file);
  }

  static createPreviewElement(file, dataUrl, config, index) {
    const col = document.createElement("div");
    col.className = "col-6 col-md-4";

    const fileSize = config.showFileSize
      ? `(${(file.size / 1024).toFixed(1)}KB)`
      : "";
    const fileName = config.showFileName ? file.name : `Image ${index + 1}`;

    col.innerHTML = `
      <div class="preview-item">
        <img src="${dataUrl}" alt="Preview" class="preview-image">
        <div class="preview-info">
          <small class="preview-name">${fileName} ${fileSize}</small>
          <button type="button" class="btn btn-sm btn-outline-danger remove-preview">
            <i class="bi bi-x"></i>
          </button>
        </div>
      </div>
    `;

    col.querySelector(".remove-preview").addEventListener("click", () => {
      col.remove();
      if (container.querySelectorAll(".preview-item").length === 0) {
        container.style.display = "none";
      }
    });

    return col;
  }

  static clearPreviews(container) {
    const grid = container.querySelector("#previewGrid") || container;
    grid.innerHTML = "";
    container.style.display = "none";
  }

  static showError(message, config) {
    if (config.onError) {
      config.onError(message);
    } else {
      console.error("Image Preview Error:", message);
    }
  }
}

/* ===== FILE VALIDATION ===== */

/**
 * File Validation Utility
 * Provides file validation functionality
 */
class FileValidation {
  static validateFile(
    file,
    maxSize = 5 * 1024 * 1024,
    allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif"]
  ) {
    if (!file) return { valid: false, error: "No file selected" };

    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds ${maxSize / (1024 * 1024)}MB limit`,
      };
    }

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: `File type ${file.type} not allowed` };
    }

    return { valid: true };
  }

  static setupFileInput(inputSelector, options = {}) {
    const input = document.querySelector(inputSelector);
    if (!input) return;

    const config = {
      maxSize: 5 * 1024 * 1024,
      allowedTypes: ["image/jpeg", "image/jpg", "image/png", "image/gif"],
      ...options,
    };

    input.addEventListener("change", (e) => {
      const files = Array.from(e.target.files);
      files.forEach((file) => {
        const validation = this.validateFile(
          file,
          config.maxSize,
          config.allowedTypes
        );
        if (!validation.valid && config.onError) {
          config.onError(validation.error);
        }
      });
    });
  }
}

/* ===== LAZY LOADING ===== */

/**
 * Lazy Loading Utility
 * Provides lazy loading functionality for images
 */
class LazyLoader {
  static initialize() {
    if ("IntersectionObserver" in window) {
      this.setupIntersectionObserver();
    } else {
      this.loadAllImages();
    }
  }

  static setupIntersectionObserver() {
    const imageObserver = new IntersectionObserver(
      (entries, observer) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target;
            this.loadImage(img);
            observer.unobserve(img);
          }
        });
      },
      {
        rootMargin: "50px 0px",
      }
    );

    document.querySelectorAll("img[data-src]").forEach((img) => {
      imageObserver.observe(img);
    });
  }

  static loadImage(img) {
    if (img.dataset.src) {
      img.src = img.dataset.src;
      img.classList.add("loaded");
      img.removeAttribute("data-src");
    }
  }

  static loadAllImages() {
    document.querySelectorAll("img[data-src]").forEach((img) => {
      this.loadImage(img);
    });
  }
}

/* ===== LOCATION OPERATIONS ===== */

/**
 * Location Operations Utility
 * Provides location search and selection functionality for forms
 */
class LocationOperations {
  constructor() {
    this.state = "INITIAL";
    this.selectedLocation = null;
    this.map = null;
    this.marker = null;
    this.tempCoordinates = null;
    this.lastSuggestedName = "";
    this.searchTimeout = null;
    this.nameValidationTimeout = null;
    this.mapSearchTimeout = null;
    this.elements = {};

    this.init();
  }

  static initialize() {
    console.log("Location operations initialized");
    return new LocationOperations();
  }

  init() {
    try {
      this.initializeElements();
      this.bindEventListeners();
      this.setupMapIcons();
      console.log("LocationOperations initialized successfully");
    } catch (error) {
      console.error("LocationOperations initialization failed:", error);
    }
  }

  initializeElements() {
    // Cache DOM elements
    this.elements = {
      searchSection: document.getElementById("locationSearchSection"),
      selectedSection: document.getElementById("selectedLocationSection"),
      mapSection: document.getElementById("mapSection"),
      searchInput: document.getElementById("locationSearch"),
      searchButton: document.getElementById("searchLocationBtn"),
      resultsContainer: document.getElementById("searchResults"),
      resultsList: document.getElementById("resultsList"),
      locationInput: document.getElementById("location"),
      changeLocationBtn: document.getElementById("changeLocationBtn"),
      editMapLocationBtn: document.getElementById("editMapLocationBtn"),
      mapSearchGroup: document.getElementById("mapSearchGroup"),
      mapSearch: document.getElementById("mapSearch"),
      mapSuggestions: document.getElementById("mapSuggestions"),
      coordinatesStatus: document.getElementById("coordinatesStatus"),
      coordinatesText: document.getElementById("coordinatesText"),
      newLocationNameGroup: document.getElementById("newLocationNameGroup"),
      newLocationName: document.getElementById("newLocationName"),
      latitudeInput: document.getElementById("latitude"),
      longitudeInput: document.getElementById("longitude"),
    };
  }

  bindEventListeners() {
    // Location search
    if (this.elements.searchButton) {
      this.elements.searchButton.addEventListener("click", () => {
        this.performLocationSearch();
      });
    }

    if (this.elements.searchInput) {
      this.elements.searchInput.addEventListener("keypress", (e) => {
        if (e.key === "Enter") {
          e.preventDefault();
          this.performLocationSearch();
        }
      });
    }

    // Change location button
    if (this.elements.changeLocationBtn) {
      this.elements.changeLocationBtn.addEventListener("click", () => {
        this.showSearchSection();
        this.setState("INITIAL");
      });
    }

    // Edit map location button
    if (this.elements.editMapLocationBtn) {
      this.elements.editMapLocationBtn.addEventListener("click", () => {
        this.enableMapEditing();
      });
    }

    // Map search
    if (this.elements.mapSearch) {
      this.elements.mapSearch.addEventListener("input", (e) => {
        this.handleMapSearchInput(e.target.value);
      });
    }

    // New location name validation
    if (this.elements.newLocationName) {
      this.elements.newLocationName.addEventListener("input", (e) => {
        this.validateNewLocationName(e.target.value);
      });
    }
  }

  setupMapIcons() {
    // Setup custom map icons if needed
    if (typeof L !== "undefined") {
      this.redIcon = L.divIcon({
        className: "custom-div-icon",
        html: '<div style="background-color: #dc3545; width: 20px; height: 20px; border-radius: 50%; border: 2px solid white;"></div>',
        iconSize: [20, 20],
        iconAnchor: [10, 10],
      });
    }
  }

  async performLocationSearch() {
    const query = this.elements.searchInput?.value?.trim();
    if (!query) {
      console.warn("Empty search query");
      return;
    }

    if (query.length < 3) {
      console.warn("Search query too short:", query);
      this.showError(
        "Please enter at least 3 characters to search for locations."
      );
      return;
    }

    console.log("Performing location search for:", query);

    try {
      this.showLoading(this.elements.resultsContainer);

      // Search database for existing locations
      const response = await fetch(
        `/location/search?query=${encodeURIComponent(query)}`
      );
      const results = await response.json();

      console.log("Search results:", results);
      this.displaySearchResults(results);
    } catch (error) {
      console.error("Location search error:", error);
      this.showError("Failed to search locations. Please try again.");
    } finally {
      this.hideLoading(this.elements.resultsContainer);
    }
  }

  displaySearchResults(results) {
    if (!this.elements.resultsList) return;

    this.elements.resultsList.innerHTML = "";

    if (results && results.length > 0) {
      results.forEach((location) => {
        const item = this.createResultItem(location, "existing");
        this.elements.resultsList.appendChild(item);
      });
      this.showSearchResults();
    } else {
      this.elements.resultsList.innerHTML =
        '<div class="p-2 text-muted">No existing locations found</div>';
      this.showSearchResults();
    }
  }

  createResultItem(location, type) {
    const item = document.createElement("div");
    item.className = "result-item";
    item.innerHTML = `
      <div class="result-info">
        <div class="result-icon">
          <i class="bi ${type === "existing" ? "bi-geo-alt" : "bi-map"}"></i>
        </div>
        <div class="result-details">
          <h6>${location.name}</h6>
          <small>${
            type === "existing" ? "Existing Location" : "New Location"
          }</small>
        </div>
      </div>
      <div class="result-type">${type === "existing" ? "Database" : "Map"}</div>
    `;

    item.addEventListener("click", () => {
      if (type === "existing") {
        this.selectExistingLocation(location.name);
      } else {
        this.selectMapLocation(location);
      }
    });

    return item;
  }

  async selectExistingLocation(locationName) {
    try {
      this.showLoading(this.elements.selectedSection);

      // Get coordinates for existing location
      const response = await fetch(
        `/api/location-coords?name=${encodeURIComponent(locationName)}`
      );
      const data = await response.json();

      this.selectedLocation = {
        name: locationName,
        type: "existing",
        lat: data.lat,
        lng: data.lng,
      };

      this.updateLocationDisplay();
      this.initializeMap();
      this.hideSearchSection();
      this.setState("SELECTED_EXISTING");
    } catch (error) {
      console.error("Error selecting existing location:", error);
      this.showError("Failed to load location details.");
    } finally {
      this.hideLoading(this.elements.selectedSection);
    }
  }

  showSearchResults() {
    if (this.elements.resultsContainer) {
      this.elements.resultsContainer.style.display = "block";
    }
  }

  hideSearchResults() {
    if (this.elements.resultsContainer) {
      this.elements.resultsContainer.style.display = "none";
    }
  }

  showSearchSection() {
    if (this.elements.searchSection) {
      this.elements.searchSection.style.display = "block";
    }
    this.hideSelectedSection();
    this.hideMapSection();
  }

  hideSearchSection() {
    if (this.elements.searchSection) {
      this.elements.searchSection.style.display = "none";
    }
    this.hideSearchResults();
  }

  showSelectedSection() {
    if (this.elements.selectedSection) {
      this.elements.selectedSection.style.display = "block";
    }
  }

  hideSelectedSection() {
    if (this.elements.selectedSection) {
      this.elements.selectedSection.style.display = "none";
    }
  }

  showMapSection() {
    if (this.elements.mapSection) {
      this.elements.mapSection.style.display = "block";
    }
  }

  hideMapSection() {
    if (this.elements.mapSection) {
      this.elements.mapSection.style.display = "none";
    }
  }

  updateLocationDisplay() {
    if (this.elements.locationInput && this.selectedLocation) {
      this.elements.locationInput.value = this.selectedLocation.name;
    }

    // Update hidden coordinate inputs
    if (this.selectedLocation) {
      if (this.elements.latitudeInput) {
        this.elements.latitudeInput.value = this.selectedLocation.lat;
      }
      if (this.elements.longitudeInput) {
        this.elements.longitudeInput.value = this.selectedLocation.lng;
      }
    }

    this.showSelectedSection();
  }

  initializeMap() {
    if (!this.selectedLocation || typeof L === "undefined") return;

    const mapElement = document.getElementById("map");
    if (!mapElement) return;

    try {
      // Remove existing map if any
      if (this.map) {
        this.map.remove();
      }

      this.map = L.map("map").setView(
        [this.selectedLocation.lat, this.selectedLocation.lng],
        15
      );

      L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png", {
        maxZoom: 19,
        attribution:
          '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
      }).addTo(this.map);

      this.marker = L.marker(
        [this.selectedLocation.lat, this.selectedLocation.lng],
        { icon: this.redIcon }
      ).addTo(this.map);

      this.showMapSection();
    } catch (error) {
      console.error("Error initializing map:", error);
    }
  }

  showLoading(element) {
    if (element) {
      element.innerHTML = '<div class="text-center p-3">Loading...</div>';
    }
  }

  hideLoading(element) {
    // Loading will be replaced by actual content
  }

  showError(message) {
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage(message, "danger");
    } else {
      console.error(message);
      alert(message);
    }
  }

  setState(state) {
    console.log(`LocationOperations: ${this.state} -> ${state}`);
    this.state = state;
  }
}

/* ===== JOURNEY OPERATIONS ===== */

/**
 * Journey Operations Utility
 * Handles journey-related operations like edit, delete, follow, etc.
 */
class JourneyOperations {
  constructor() {
    this.initializeEventHandlers();
  }

  initializeEventHandlers() {
    console.log("Initializing Journey Operations event handlers...");

    // Follow/Unfollow button - with proper event delegation
    document.addEventListener("click", (e) => {
      const followButton = e.target.closest('[data-action="toggle-follow"]');
      if (followButton) {
        e.preventDefault();
        const journeyId = followButton.dataset.journeyId;
        this.toggleFollow(followButton, journeyId);
      }
    });

    // Edit journey button - with proper event delegation
    document.addEventListener("click", (e) => {
      const editButton = e.target.closest('[data-action="edit-journey"]');
      if (editButton) {
        e.preventDefault();
        const journeyId = editButton.dataset.journeyId;
        this.openEditModal(journeyId);
      }
    });

    // Hide/Unhide journey button - with proper event delegation
    document.addEventListener("click", (e) => {
      const hideButton = e.target.closest('[data-action="toggle-hidden"]');
      if (hideButton) {
        e.preventDefault();
        const journeyId = hideButton.dataset.journeyId;
        this.toggleHidden(hideButton, journeyId);
      }
    });

    // View edit history - with proper event delegation
    document.addEventListener("click", (e) => {
      const historyButton = e.target.closest(
        '[data-action="view-edit-history"]'
      );
      if (historyButton) {
        e.preventDefault();
        const journeyId = historyButton.dataset.journeyId;
        const journeyTitle = historyButton.dataset.journeyTitle;
        if (typeof openEditHistoryModal === "function") {
          openEditHistoryModal(journeyId, journeyTitle);
        } else {
          console.error("openEditHistoryModal function not available");
        }
      }
    });

    // Create event button - with proper event delegation
    document.addEventListener("click", (e) => {
      const createButton = e.target.closest('[data-action="create-event"]');
      if (createButton) {
        e.preventDefault();
        const journeyId = createButton.dataset.journeyId;
        this.openCreateEventModal(journeyId);
      }
    });

    // Delete journey button - with proper event delegation
    document.addEventListener("click", (e) => {
      const deleteButton = e.target.closest('[data-action="delete-journey"]');
      if (deleteButton) {
        e.preventDefault();
        const journeyId = deleteButton.dataset.journeyId;
        this.confirmDelete(journeyId);
      }
    });
  }

  async toggleFollow(button, journeyId) {
    if (!journeyId) {
      console.error("Journey ID is required for follow operation");
      return;
    }

    console.log(`Toggling follow for journey ${journeyId}`);

    try {
      button.disabled = true;

      const response = await fetch(`/journey/api/${journeyId}/toggle-follow`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      const result = await response.json();
      console.log(`Follow API response:`, result);

      if (result.success) {
        this.updateFollowButton(button, result.is_following);
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "success");
        }
      } else {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "danger");
        }
      }
    } catch (error) {
      console.error("Error toggling follow:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage("Failed to update follow status", "danger");
      }
    }
  }

  updateFollowButton(button, isFollowing) {
    const icon = button.querySelector("i");
    const text = button.querySelector(".btn-text");

    if (isFollowing) {
      button.classList.remove("btn-outline-primary");
      button.classList.add("btn-primary");
      if (icon) icon.className = "bi bi-heart-fill me-1";
      if (text) text.textContent = "Following";
    } else {
      button.classList.remove("btn-primary");
      button.classList.add("btn-outline-primary");
      if (icon) icon.className = "bi bi-heart me-1";
      if (text) text.textContent = "Follow";
    }

    button.disabled = false;
  }

  async openEditModal(journeyId) {
    if (!journeyId) {
      console.error("Journey ID is required for edit operation");
      return;
    }

    console.log(`Opening edit modal for journey ${journeyId}`);

    try {
      const response = await fetch(`/journey/private/${journeyId}/edit`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const formHtml = await response.text();

      if (typeof showModal === "function") {
        showModal("Edit Journey", formHtml, {
          actionText: "Save",
          size: "large",
          onAction: async () => {
            return await this.submitEditForm(journeyId);
          },
        });
      } else {
        console.error("showModal function not available");
      }
    } catch (error) {
      console.error("Error opening edit modal:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage("Failed to load edit form", "danger");
      }
    }
  }

  async submitEditForm(journeyId) {
    console.log(`Submitting edit form for journey ${journeyId}`);

    try {
      const form = document.getElementById("editJourneyForm");
      if (!form) {
        throw new Error("Edit form not found");
      }

      // Validate form using Bootstrap validation
      form.classList.add("was-validated");

      if (!form.checkValidity()) {
        const firstInvalidField = form.querySelector(":invalid");
        if (firstInvalidField) {
          firstInvalidField.focus();
        }
        return false;
      }

      const formData = new FormData(form);

      const response = await fetch(`/journey/private/${journeyId}/edit`, {
        method: "POST",
        body: formData,
        headers: {
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      const result = await response.json();

      if (result.success) {
        // Handle "no changes" case differently - show info message without DOM updates
        if (result.no_changes) {
          console.log(
            "🔍 NO CHANGES DETECTED in journey-detail-bundle.js - showing info message without DOM updates"
          );
          if (typeof window.showFlashMessage === "function") {
            window.showFlashMessage(result.message, "info");
          }

          if (typeof window.closeModal === "function") {
            window.closeModal();
          }
          return true; // Don't update DOM
        }

        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "success");
        }

        if (typeof window.closeModal === "function") {
          window.closeModal();
        }

        // Update DOM instead of reloading page
        // Include no_edits from form data since server might not return it
        const formData = new FormData(form);
        const journeyData = result.journey || {};
        journeyData.no_edits = formData.has("no_edits");
        this.updateJourneyDetailsDOM(journeyData);
      } else {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "danger");
        }
      }

      return true;
    } catch (error) {
      console.error("Error submitting edit form:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage("Failed to update journey", "danger");
      }
      return false;
    }
  }

  async toggleHidden(button, journeyId) {
    if (!journeyId) {
      console.error("Journey ID is required for hide operation");
      return;
    }

    console.log(`Toggling hidden status for journey ${journeyId}`);

    try {
      button.disabled = true;

      const response = await fetch(`/journey/api/${journeyId}/hidden-status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      const result = await response.json();
      console.log(`API response:`, result);

      if (result.success) {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "success");
        }

        // Update DOM badges instead of reloading page
        console.log("Success! Updating DOM badges...");
        this.updateHiddenStatusBadges(result.is_hidden);
        this.updateHiddenButtonText(button, result.is_hidden);
      } else {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "danger");
        }
      }
    } catch (error) {
      console.error("Error toggling hidden status:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage("Failed to update journey status", "danger");
      }
    }
  }

  // Update hidden status badges in the DOM
  updateHiddenStatusBadges(isHidden) {
    console.log(`Updating hidden badges - isHidden: ${isHidden}`);

    const hiddenBadges = document.querySelectorAll(".hidden-badge");

    if (isHidden) {
      if (hiddenBadges.length === 0) {
        // Create hidden badge if it doesn't exist
        const visibilityBadge = document.querySelector(".visibility-badge");
        if (visibilityBadge) {
          const hiddenBadge = document.createElement("span");
          hiddenBadge.className =
            "badge bg-warning text-dark rounded-pill py-1 px-3 ms-1 hidden-badge";
          hiddenBadge.textContent = "Hidden";
          visibilityBadge.parentNode.insertBefore(
            hiddenBadge,
            visibilityBadge.nextSibling
          );
          console.log("Created hidden badge");
        }
      } else {
        // Show existing hidden badges
        hiddenBadges.forEach((badge) => {
          badge.style.display = "inline-block";
          badge.classList.remove("d-none");
        });
        console.log("Showed existing hidden badges");
      }
    } else {
      // Remove hidden badges
      hiddenBadges.forEach((badge) => {
        badge.remove();
      });
      console.log("Removed hidden badges");
    }

    console.log(`Hidden badges updated`);
  }

  // Update the hide/unhide button text
  updateHiddenButtonText(button, isHidden) {
    const buttonText = button.querySelector(".btn-text");
    if (buttonText) {
      buttonText.textContent = isHidden ? "Unhide Journey" : "Hide Journey";
    }

    button.disabled = false;
  }

  // Update journey details in DOM after successful edit
  updateJourneyDetailsDOM(journeyData) {
    console.log("Updating journey details in DOM:", journeyData);

    // Update title
    const titleElement = document.getElementById("journeyTitle");
    if (titleElement && journeyData.title) {
      titleElement.textContent = journeyData.title;
    }

    // Update description
    const descriptionElement = document.getElementById("journeyDescription");
    if (descriptionElement && journeyData.description) {
      descriptionElement.textContent = journeyData.description;
    }

    // Update start date
    const startDateElement = document.getElementById("journeyStartDate");
    if (startDateElement && journeyData.start_date) {
      // Format the date properly
      const date = new Date(journeyData.start_date);
      const formattedDate = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
      startDateElement.textContent = formattedDate;
    }

    // Update visibility badge
    const visibilityBadge = document.querySelector(".visibility-badge");
    if (visibilityBadge && journeyData.visibility) {
      visibilityBadge.className = `badge visibility-badge ${
        journeyData.visibility === "public" ||
        journeyData.visibility === "published"
          ? "bg-success"
          : "bg-secondary"
      } rounded-pill py-1 px-3`;

      if (
        journeyData.visibility === "public" ||
        journeyData.visibility === "published"
      ) {
        visibilityBadge.textContent =
          journeyData.visibility.charAt(0).toUpperCase() +
          journeyData.visibility.slice(1);
      } else {
        visibilityBadge.textContent = "Private";
      }
    }

    // Update no_edits badge
    const noEditsBadge = document.querySelector(".no-edits-badge");
    if (journeyData.hasOwnProperty("no_edits")) {
      if (journeyData.no_edits) {
        if (!noEditsBadge) {
          // Create the badge if it doesn't exist
          const badgeContainer =
            document.querySelector(".visibility-badge").parentElement;
          const newBadge = document.createElement("span");
          newBadge.className =
            "badge bg-warning text-dark rounded-pill py-1 px-3 ms-1 no-edits-badge";
          newBadge.title = "This journey is protected from staff edits";
          newBadge.innerHTML = '<i class="bi bi-shield-lock me-1"></i>No Edits';
          badgeContainer.appendChild(newBadge);
        } else {
          noEditsBadge.style.display = "inline-block";
        }
      } else {
        if (noEditsBadge) {
          noEditsBadge.style.display = "none";
        }
      }
    }

    console.log("Journey details updated in DOM");
  }

  /**
   * Confirm and delete journey
   */
  confirmDelete(journeyId) {
    if (typeof showModal === "function") {
      showModal(
        "Delete Journey",
        "Are you sure you want to delete this journey? This action cannot be undone.",
        {
          actionText: "Delete",
          onAction: () => {
            this.deleteJourney(journeyId);
          },
        }
      );
    } else {
      // Fallback to browser confirm
      if (
        confirm(
          "Are you sure you want to delete this journey? This action cannot be undone."
        )
      ) {
        this.deleteJourney(journeyId);
      }
    }
  }

  /**
   * Delete journey
   */
  async deleteJourney(journeyId) {
    try {
      // Submit the delete form if it exists
      const deleteForm =
        document.getElementById(`deleteJourneyForm_${journeyId}`) ||
        document.getElementById("deleteJourneyForm");

      if (deleteForm) {
        deleteForm.submit();
      } else {
        console.error("Delete form not found");
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(
            "Delete form not found. Please refresh and try again.",
            "danger"
          );
        }
      }
    } catch (error) {
      console.error("Error deleting journey:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "An error occurred. Please try again.",
          "danger"
        );
      }
    }
  }

  // Open create event modal
  async openCreateEventModal(journeyId) {
    if (!journeyId) {
      console.error("Journey ID is required for create event operation");
      return;
    }

    console.log(`Opening create event modal for journey ${journeyId}`);

    try {
      const response = await fetch(`/event/new/${journeyId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const formHtml = await response.text();

      if (typeof showModal === "function") {
        showModal("Add New Event", formHtml, {
          actionText: "Create",
          onAction: async () => {
            return await this.submitCreateEventForm();
          },
        });

        // Initialize location search and file validation after modal is shown
        setTimeout(() => {
          this.initializeLocationSearch();
          this.initializeFileValidation();
        }, 200);
      } else {
        console.error("showModal function not available");
      }
    } catch (error) {
      console.error("Error opening create event modal:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage("Failed to load create event form", "danger");
      }
    }
  }

  // Submit create event form
  async submitCreateEventForm() {
    console.log("Submitting create event form");

    const form = document.getElementById("createEventForm");
    if (!form) {
      throw new Error("Create event form not found");
    }

    // Validate form using Bootstrap validation
    form.classList.add("was-validated");

    if (!form.checkValidity()) {
      const firstInvalidField = form.querySelector(":invalid");
      if (firstInvalidField) {
        firstInvalidField.focus();
      }
      return false;
    }

    // Check file validation errors
    const fileInput = document.getElementById("images");
    if (fileInput) {
      const fileErrors = JSON.parse(fileInput.dataset.validationErrors || "[]");
      if (fileErrors.length > 0) {
        // Scroll to file input to show errors
        fileInput.scrollIntoView({ behavior: "smooth", block: "center" });

        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(
            "Please fix file validation errors before submitting.",
            "danger"
          );
        }
        return false;
      }
    }

    // Check if location is selected
    const locationInput = document.getElementById("location");
    const latInput = document.getElementById("latitude");
    const lngInput = document.getElementById("longitude");

    if (!locationInput?.value || !latInput?.value || !lngInput?.value) {
      showFlashMessage("Please select a location first.", "warning");
      return false;
    }

    try {
      const formData = new FormData(form);

      const response = await fetch(form.action, {
        method: "POST",
        body: formData,
        headers: {
          "X-Requested-With": "XMLHttpRequest",
        },
      });

      // Check if response is JSON
      const contentType = response.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        const text = await response.text();
        console.error("Server returned non-JSON response:", text);
        throw new Error(
          "Server returned an error page instead of JSON response"
        );
      }

      const result = await response.json();

      if (result.success) {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "success");
        }

        if (typeof window.closeModal === "function") {
          window.closeModal();
        }

        // Reload page to show new event (for now - could be improved with DOM updates)
        window.location.reload();
      } else {
        if (typeof window.showFlashMessage === "function") {
          window.showFlashMessage(result.message, "danger");
        }
      }

      return true;
    } catch (error) {
      console.error("Error submitting create event form:", error);
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage("Failed to create event", "danger");
      }
      return false;
    }
  }

  // Initialize location search functionality in create event modal
  initializeLocationSearch() {
    console.log("Initializing location search functionality");

    // Wait for modal to be fully loaded and visible
    const initializeWhenReady = () => {
      // Check if all required elements exist
      const requiredElements = [
        "locationSearch",
        "searchLocationBtn",
        "searchResults",
        "resultsList",
        "selectedLocationSection",
        "location",
        "changeLocationBtn",
        "editMapLocationBtn",
        "mapSection",
        "mapSearchGroup",
        "mapSearch",
        "mapSuggestions",
        "coordinatesStatus",
        "coordinatesText",
        "newLocationNameGroup",
        "newLocationName",
        "latitude",
        "longitude",
      ];

      const missingElements = requiredElements.filter(
        (id) => !document.getElementById(id)
      );

      if (missingElements.length > 0) {
        console.log(
          `⏳ Waiting for modal elements: ${missingElements.join(", ")}`
        );
        return false;
      }

      // Check if Leaflet is available
      if (typeof L === "undefined") {
        console.log("Waiting for Leaflet library to load");
        return false;
      }

      // All dependencies are ready, initialize LocationSelector
      try {
        this.locationSelector = new LocationSelector();
        console.log("LocationSelector initialized successfully");
        return true;
      } catch (error) {
        console.error("LocationSelector initialization failed:", error);
        throw error;
      }
    };

    // Try immediate initialization
    if (initializeWhenReady()) {
      return;
    }

    // If not ready, wait for modal to be shown
    // The modal content is loaded into the common modal container
    const modalElement =
      document.getElementById("commonModal") ||
      document.querySelector(".modal");

    if (modalElement) {
      console.log(
        "🔍 Setting up modal observer for LocationSelector initialization"
      );

      // Use MutationObserver to detect when modal content changes
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          // Check for added nodes (modal content being inserted)
          if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
            // Check if the added content contains our required elements
            const hasLocationElements = Array.from(mutation.addedNodes).some(
              (node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  return (
                    node.querySelector("#locationSearch") ||
                    node.id === "locationSearch"
                  );
                }
                return false;
              }
            );

            if (hasLocationElements) {
              console.log("Modal content with location elements detected");
              setTimeout(() => {
                if (initializeWhenReady()) {
                  observer.disconnect();
                }
              }, 100);
            }
          }

          // Also check for attribute changes (modal becoming visible)
          if (
            mutation.type === "attributes" &&
            (mutation.attributeName === "style" ||
              mutation.attributeName === "class")
          ) {
            const modal = mutation.target;
            if (
              modal.classList.contains("show") ||
              (modal.style.display !== "none" && modal.offsetParent !== null)
            ) {
              console.log(
                "🔍 Modal became visible, checking for location elements"
              );
              setTimeout(() => {
                if (initializeWhenReady()) {
                  observer.disconnect();
                }
              }, 150);
            }
          }
        });
      });

      // Observe both content changes and attribute changes
      observer.observe(modalElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["style", "class"],
      });

      // Also try after a short delay as fallback
      setTimeout(() => {
        if (initializeWhenReady()) {
          observer.disconnect();
        }
      }, 800);

      // Additional fallback - try again after longer delay
      setTimeout(() => {
        if (initializeWhenReady()) {
          observer.disconnect();
        }
      }, 1500);
    } else {
      console.warn(
        "❌ Modal container not found, trying direct initialization after delay"
      );
      // Final fallback - try after a longer delay
      setTimeout(() => {
        initializeWhenReady();
      }, 1000);
    }
  }

  // Initialize file validation for event images
  initializeFileValidation() {
    console.log("Initializing file validation for event images");

    const fileInput = document.getElementById("images");
    if (!fileInput) {
      console.log(
        "File input not found, skipping file validation initialization"
      );
      return;
    }

    // Get premium status from the file input data attribute
    const isPremium = fileInput.dataset.premium === "true";
    const maxFiles = isPremium ? 10 : 1;
    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif"];

    // Create or find validation message container
    let validationContainer = document.getElementById(
      "imagesValidationFeedback"
    );
    if (!validationContainer) {
      // Create validation container after the file input
      validationContainer = document.createElement("div");
      validationContainer.id = "imagesValidationFeedback";
      validationContainer.className = "validation-feedback";
      validationContainer.style.display = "none";
      fileInput.parentNode.insertBefore(
        validationContainer,
        fileInput.nextSibling
      );
    }

    // Real-time validation function
    const validateFiles = (files) => {
      const errors = [];
      const fileArray = Array.from(files);

      // Check file count
      if (fileArray.length > maxFiles) {
        errors.push(
          `You can only select up to ${maxFiles} image${
            maxFiles > 1 ? "s" : ""
          }.${
            !isPremium ? " Upgrade to premium for multiple image uploads." : ""
          }`
        );
      }

      // Check individual files
      fileArray.forEach((file, index) => {
        // Check file size
        if (file.size > maxSizeBytes) {
          const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
          errors.push(
            `"${file.name}" is ${fileSizeMB}MB. Maximum allowed size is 5MB.`
          );
        }

        // Check file type
        const fileType = file.type.toLowerCase();
        if (!allowedTypes.includes(fileType)) {
          errors.push(
            `"${file.name}" has an invalid file type. Only PNG, JPG, JPEG, and GIF files are allowed.`
          );
        }
      });

      return errors;
    };

    // Update validation display
    const updateValidationDisplay = (errors) => {
      if (errors.length > 0) {
        validationContainer.innerHTML = `
          <div class="alert alert-danger py-2 px-3 mb-2" style="font-size: 12px;">
            <i class="bi bi-exclamation-triangle me-2"></i>
            <strong>File Validation Errors:</strong>
            <ul class="mb-0 mt-1" style="padding-left: 20px;">
              ${errors.map((error) => `<li>${error}</li>`).join("")}
            </ul>
          </div>
        `;
        validationContainer.style.display = "block";

        // Add invalid class to file input
        fileInput.classList.add("is-invalid");
        fileInput.classList.remove("is-valid");

        // Update form group styling
        const formGroup = fileInput.closest(".form-group");
        if (formGroup) {
          formGroup.classList.add("has-error");
          formGroup.classList.remove("has-success");
        }
      } else {
        // Clear validation messages
        validationContainer.style.display = "none";
        validationContainer.innerHTML = "";

        // Add valid class to file input if files are selected
        if (fileInput.files.length > 0) {
          fileInput.classList.remove("is-invalid");
          fileInput.classList.add("is-valid");

          // Update form group styling
          const formGroup = fileInput.closest(".form-group");
          if (formGroup) {
            formGroup.classList.remove("has-error");
            formGroup.classList.add("has-success");
          }

          // Show success message
          validationContainer.innerHTML = `
            <div class="alert alert-success py-2 px-3 mb-2" style="font-size: 12px;">
              <i class="bi bi-check-circle me-2"></i>
              ${fileInput.files.length} image${
            fileInput.files.length > 1 ? "s" : ""
          } selected successfully.
            </div>
          `;
          validationContainer.style.display = "block";
        } else {
          // No files selected - neutral state
          fileInput.classList.remove("is-invalid", "is-valid");
          const formGroup = fileInput.closest(".form-group");
          if (formGroup) {
            formGroup.classList.remove("has-error", "has-success");
          }
        }
      }
    };

    // Add event listener for file input changes
    fileInput.addEventListener("change", (event) => {
      const files = event.target.files;
      console.log(`File input changed: ${files.length} files selected`);

      const errors = validateFiles(files);
      updateValidationDisplay(errors);

      // Store validation result for form submission
      fileInput.dataset.validationErrors = JSON.stringify(errors);
    });

    // Initialize validation state
    fileInput.dataset.validationErrors = JSON.stringify([]);

    // Enhanced form submission validation
    const form = document.getElementById("createEventForm");
    if (form) {
      // Override form validation to include file validation
      const submitHandler = (event) => {
        const fileErrors = JSON.parse(
          fileInput.dataset.validationErrors || "[]"
        );
        if (fileErrors.length > 0) {
          event.preventDefault();
          event.stopPropagation();

          // Scroll to file input to show errors
          fileInput.scrollIntoView({ behavior: "smooth", block: "center" });

          // Show flash message
          if (typeof window.showFlashMessage === "function") {
            window.showFlashMessage(
              "Please fix file validation errors before submitting.",
              "danger"
            );
          }

          return false;
        }
      };

      // Add event listener with high priority
      form.addEventListener("submit", submitHandler, true);
    }

    console.log("File validation initialized successfully");
  }
}

/**
 * Complete LocationSelector Class - Restored from Old Implementation
 *
 * This implementation uses a clear state machine approach:
 * 1. SEARCHING - User is searching for locations
 * 2. SELECTED_EXISTING - User selected an existing location from database
 * 3. CREATING_NEW - User is creating a new location with map coordinates
 */
class LocationSelector {
  constructor() {
    this.state = "INITIAL";
    this.selectedLocation = null;
    this.map = null;
    this.marker = null;
    this.tempCoordinates = null;
    this.lastSuggestedName = "";
    this.searchTimeout = null;
    this.nameValidationTimeout = null;
    this.mapSearchTimeout = null;

    try {
      this.initializeElements();
      this.bindEvents();
      this.setupMapIcons();
      console.log("LocationSelector initialized successfully");
    } catch (error) {
      console.error("LocationSelector initialization failed:", error);
      throw error;
    }
  }

  initializeElements() {
    // Get all required elements
    this.elements = {
      locationSearch: document.getElementById("locationSearch"),
      searchBtn: document.getElementById("searchLocationBtn"),
      searchResults: document.getElementById("searchResults"),
      resultsList: document.getElementById("resultsList"),
      selectedSection: document.getElementById("selectedLocationSection"),
      locationInput: document.getElementById("location"),
      changeLocationBtn: document.getElementById("changeLocationBtn"),
      editMapLocationBtn: document.getElementById("editMapLocationBtn"),
      mapSection: document.getElementById("mapSection"),
      mapSearchGroup: document.getElementById("mapSearchGroup"),
      mapSearch: document.getElementById("mapSearch"),
      mapSuggestions: document.getElementById("mapSuggestions"),
      coordinatesStatus: document.getElementById("coordinatesStatus"),
      coordinatesText: document.getElementById("coordinatesText"),
      newLocationNameGroup: document.getElementById("newLocationNameGroup"),
      newLocationName: document.getElementById("newLocationName"),
      latitudeInput: document.getElementById("latitude"),
      longitudeInput: document.getElementById("longitude"),
    };

    // Check for missing elements
    const missingElements = [];
    for (const [key, element] of Object.entries(this.elements)) {
      if (!element) {
        missingElements.push(key);
      }
    }

    if (missingElements.length > 0) {
      console.error("Missing required elements:", missingElements);
      throw new Error(
        `Missing required elements: ${missingElements.join(", ")}`
      );
    }

    console.log("All required elements found");
  }

  setupMapIcons() {
    if (typeof L !== "undefined") {
      this.redIcon = L.icon({
        iconUrl:
          "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png",
        shadowUrl:
          "https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png",
        iconSize: [25, 41],
        iconAnchor: [12, 41],
        popupAnchor: [1, -34],
        shadowSize: [41, 41],
      });
    }
  }

  bindEvents() {
    // Search functionality
    this.elements.searchBtn.addEventListener("click", () =>
      this.performSearch()
    );
    this.elements.locationSearch.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        e.preventDefault();
        e.stopPropagation();
        this.performSearch();
      }
    });

    // Location management
    this.elements.changeLocationBtn.addEventListener("click", () =>
      this.changeLocation()
    );
    this.elements.editMapLocationBtn.addEventListener("click", () =>
      this.editMapLocation()
    );

    // New location creation
    this.elements.newLocationName.addEventListener("input", () =>
      this.debounceNameValidation()
    );

    // Map search
    this.elements.mapSearch.addEventListener("input", () =>
      this.debounceMapSearch()
    );

    // Click outside to hide suggestions
    document.addEventListener("click", (e) => this.handleClickOutside(e));
  }

  async performSearch() {
    const query = this.elements.locationSearch.value.trim();
    if (!query) {
      this.showError("Please enter a location name or address to search.");
      return;
    }

    if (query.length < 3) {
      this.showError(
        "Please enter at least 3 characters to search for locations."
      );
      return;
    }

    // Show loading state
    this.showSearchLoading(true);

    try {
      // Search both database and map
      const [dbResults, mapResults] = await Promise.all([
        this.searchDatabase(query),
        this.searchMap(query),
      ]);

      this.displaySearchResults(dbResults, mapResults);
      this.setState("SEARCHING");
    } catch (error) {
      console.error("Search error:", error);
      this.showError(
        "Search failed. Please check your connection and try again."
      );
    } finally {
      this.showSearchLoading(false);
    }
  }

  showSearchLoading(isLoading) {
    const searchBtn = this.elements.searchBtn;
    const searchInput = this.elements.locationSearch;

    if (isLoading) {
      // Show loading state
      searchBtn.disabled = true;
      searchBtn.innerHTML =
        '<i class="bi bi-hourglass-split me-2"></i>Searching...';
      searchBtn.classList.add("loading");
      searchInput.disabled = true;

      // Hide any previous results
      this.elements.searchResults.style.display = "none";
    } else {
      // Reset to normal state
      searchBtn.disabled = false;
      searchBtn.innerHTML = '<i class="bi bi-search"></i> Search';
      searchBtn.classList.remove("loading");
      searchInput.disabled = false;
    }
  }

  async searchDatabase(query) {
    const response = await fetch(
      `/location/search?query=${encodeURIComponent(query)}`
    );
    if (!response.ok) throw new Error("Database search failed");
    return await response.json();
  }

  async searchMap(query) {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
        query
      )}&limit=1&addressdetails=1`
    );
    if (!response.ok) throw new Error("Map search failed");
    return await response.json();
  }

  displaySearchResults(dbResults, mapResults) {
    this.elements.resultsList.innerHTML = "";

    // Check if both result arrays are empty
    const hasDbResults = dbResults && dbResults.length > 0;
    const hasMapResults = mapResults && mapResults.length > 0;

    if (!hasDbResults && !hasMapResults) {
      // Show "no results found" message
      const noResultsItem = document.createElement("div");
      noResultsItem.className = "no-results-message p-3 text-center text-muted";
      noResultsItem.innerHTML = `
        <div class="mb-2">
          <i class="bi bi-search fs-4"></i>
        </div>
        <p class="mb-1 fw-medium">No locations found</p>
        <small>Try a different search <br> term or check your spelling</small>
      `;
      this.elements.resultsList.appendChild(noResultsItem);
      this.elements.searchResults.style.display = "block";
      return;
    }

    // Add database results
    dbResults.forEach((location) => {
      const item = this.createResultItem(
        location.name,
        "Existing Location",
        "bi-database",
        "existing",
        () => this.selectExistingLocation(location.name)
      );
      this.elements.resultsList.appendChild(item);
    });

    // Add map results (limited to most relevant result for better UX)
    mapResults.forEach((place, index) => {
      const cleanName = this.extractLocationName(place.display_name);
      const item = this.createResultItem(
        cleanName,
        index === 0 ? "New Location (Most Relevant)" : "New Location",
        "bi-geo-alt",
        "new",
        () => this.selectNewLocation(place, cleanName)
      );
      this.elements.resultsList.appendChild(item);
    });

    this.elements.searchResults.style.display = "block";
  }

  createResultItem(name, type, icon, category, onClick) {
    const item = document.createElement("div");
    item.className = "result-item";
    item.innerHTML = `
      <div class="result-info">
        <div class="result-icon">
          <i class="bi ${icon}"></i>
        </div>
        <div class="result-details">
          <h6>${name}</h6>
          <small>${type}</small>
        </div>
      </div>
      <span class="result-type">${category}</span>
    `;
    item.addEventListener("click", onClick);
    return item;
  }

  async selectExistingLocation(locationName) {
    try {
      this.showLoading(this.elements.selectedSection);

      // Get coordinates for existing location
      const response = await fetch(
        `/api/location-coords?name=${encodeURIComponent(locationName)}`
      );
      const data = await response.json();

      this.selectedLocation = {
        name: locationName,
        type: "existing",
        lat: data.lat,
        lng: data.lng,
      };

      this.updateLocationDisplay();
      this.initializeMap();
      this.hideSearchSection();
      this.setState("SELECTED_EXISTING");
    } catch (error) {
      console.error("Error selecting existing location:", error);
      this.showError("Failed to load location details.");
    } finally {
      this.hideLoading(this.elements.selectedSection);
    }
  }

  selectNewLocation(place, suggestedName) {
    this.tempCoordinates = {
      lat: parseFloat(place.lat),
      lng: parseFloat(place.lon),
      mapDisplayName: place.display_name,
    };

    // Show map for new location creation
    this.elements.newLocationName.value = suggestedName;
    this.initializeMapForNewLocation();
    this.hideSearchSection();
    this.setState("CREATING_NEW");

    // Enhanced UX: Auto-focus on map and prepopulate map search field
    setTimeout(() => {
      // Prepopulate the map search field with the selected location
      if (this.elements.mapSearch) {
        this.elements.mapSearch.value = place.display_name;
        console.log(
          `✨ Auto-populated map search field: ${place.display_name}`
        );
      }

      // Auto-focus on the map section by scrolling to it
      if (this.elements.mapSection) {
        this.elements.mapSection.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
        console.log("Auto-focused on map section for better UX");
      }

      // Validate the form since we now have coordinates
      this.validateNewLocationForm();
    }, 200); // Small delay to ensure map is initialized
  }

  updateLocationDisplay() {
    this.elements.locationInput.value = this.selectedLocation.name;
    this.elements.latitudeInput.value = this.selectedLocation.lat;
    this.elements.longitudeInput.value = this.selectedLocation.lng;

    this.elements.selectedSection.style.display = "block";
    this.elements.searchResults.style.display = "none";

    // Show edit map button for existing locations
    if (this.selectedLocation.type === "existing") {
      this.elements.editMapLocationBtn.style.display = "inline-block";
    }
  }

  initializeMap() {
    if (!this.selectedLocation?.lat || !this.selectedLocation?.lng) return;

    // Show only the map section for existing locations (no search interface)
    this.elements.mapSection.style.display = "block";

    // Hide map search and new location creation fields for existing locations
    this.elements.mapSearchGroup.style.display = "none";
    this.elements.newLocationNameGroup.style.display = "none";
    this.elements.coordinatesStatus.style.display = "none";

    // Remove existing map
    if (this.map) {
      this.map.remove();
    }

    // Create new map for preview only (non-interactive)
    this.map = L.map("map").setView(
      [this.selectedLocation.lat, this.selectedLocation.lng],
      13
    );

    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(this.map);

    // Add non-draggable marker for existing locations (preview only)
    this.marker = L.marker([
      this.selectedLocation.lat,
      this.selectedLocation.lng,
    ])
      .addTo(this.map)
      .bindPopup(this.selectedLocation.name);

    // Invalidate size after modal is shown
    setTimeout(() => this.map.invalidateSize(), 100);
  }

  initializeMapForNewLocation() {
    this.elements.mapSection.style.display = "block";
    this.elements.mapSearchGroup.style.display = "block";
    this.elements.newLocationNameGroup.style.display = "block";
    this.elements.selectedSection.style.display = "none";
    this.elements.searchResults.style.display = "none";

    // Update help text to be contextually appropriate
    const helpText = this.elements.mapSearchGroup.querySelector(".input-help");
    helpText.innerHTML =
      '<i class="bi bi-info-circle"></i> Search for address and click on map to set location';
    helpText.className = "input-help";

    // Remove existing map
    if (this.map) {
      this.map.remove();
    }

    // Create new map
    const lat = this.tempCoordinates.lat;
    const lng = this.tempCoordinates.lng;

    this.map = L.map("map").setView([lat, lng], 13);

    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: "© OpenStreetMap contributors",
    }).addTo(this.map);

    // Add draggable marker
    this.marker = L.marker([lat, lng], { draggable: true })
      .addTo(this.map)
      .bindPopup("New Location");

    // Update coordinates when marker is moved
    this.marker.on("dragend", (e) => {
      const position = e.target.getLatLng();
      this.tempCoordinates.lat = position.lat;
      this.tempCoordinates.lng = position.lng;

      // Coordinates are set but hidden from users (for internal use only)
      this.elements.coordinatesText.textContent = "Location coordinates set";
      // Keep coordinates status hidden from users

      // Perform reverse geocoding to update search field
      this.reverseGeocode(position.lat, position.lng);

      this.validateNewLocationForm();
    });

    // Update marker when map is clicked
    this.map.on("click", (e) => {
      this.marker.setLatLng(e.latlng);
      this.tempCoordinates.lat = e.latlng.lat;
      this.tempCoordinates.lng = e.latlng.lng;

      // Coordinates are set but hidden from users (for internal use only)
      this.elements.coordinatesText.textContent = "Location coordinates set";
      // Keep coordinates status hidden from users

      // Perform reverse geocoding to update search field
      this.reverseGeocode(e.latlng.lat, e.latlng.lng);

      this.validateNewLocationForm();
    });

    setTimeout(() => this.map.invalidateSize(), 100);
  }

  debounceMapSearch() {
    clearTimeout(this.mapSearchTimeout);
    this.mapSearchTimeout = setTimeout(() => this.performMapSearch(), 300);
  }

  async performMapSearch() {
    const query = this.elements.mapSearch.value.trim();
    if (query.length < 3) {
      this.elements.mapSuggestions.style.display = "none";
      return;
    }

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
          query
        )}&limit=5`
      );
      const results = await response.json();

      this.displayMapSuggestions(results);
    } catch (error) {
      console.error("Map search error:", error);
    }
  }

  displayMapSuggestions(results) {
    this.elements.mapSuggestions.innerHTML = "";

    if (results.length === 0) {
      this.elements.mapSuggestions.style.display = "none";
      return;
    }

    results.forEach((place) => {
      const item = document.createElement("div");
      item.className = "suggestion-item";
      item.textContent = place.display_name;
      item.addEventListener("click", () => this.selectMapSuggestion(place));
      this.elements.mapSuggestions.appendChild(item);
    });

    // Show the suggestions dropdown
    this.elements.mapSuggestions.style.display = "block";
  }

  selectMapSuggestion(place) {
    const lat = parseFloat(place.lat);
    const lng = parseFloat(place.lon);

    // Update map view and marker
    this.map.setView([lat, lng], 13);
    this.marker.setLatLng([lat, lng]);

    // Update coordinates
    this.tempCoordinates.lat = lat;
    this.tempCoordinates.lng = lng;

    // Update search field and hide suggestions
    this.elements.mapSearch.value = place.display_name;
    this.elements.mapSuggestions.style.display = "none";

    // Coordinates are set but hidden from users (for internal use only)
    this.elements.coordinatesText.textContent = "Location coordinates set";
    // Keep coordinates status hidden from users

    // Enable save button since coordinates are now selected
    this.validateNewLocationForm();
  }

  validateNewLocationForm() {
    const name = this.elements.newLocationName.value.trim();
    const hasCoordinates =
      this.tempCoordinates &&
      this.tempCoordinates.lat &&
      this.tempCoordinates.lng;

    // Update form fields when both name and coordinates are available
    if (name && hasCoordinates) {
      // Check name uniqueness before populating form fields
      this.checkLocationNameUniqueness(name);
    } else if (!hasCoordinates) {
      // Clear form fields if coordinates missing
      this.elements.locationInput.value = "";
      this.elements.latitudeInput.value = "";
      this.elements.longitudeInput.value = "";

      const helpText =
        this.elements.newLocationNameGroup.querySelector(".input-help");
      helpText.innerHTML =
        '<i class="bi bi-exclamation-triangle text-warning"></i> Please search for an address and select coordinates from the map';
      helpText.className = "input-help text-warning";
    } else {
      // Clear form fields if name missing
      this.elements.locationInput.value = "";
      this.elements.latitudeInput.value = "";
      this.elements.longitudeInput.value = "";

      const helpText =
        this.elements.newLocationNameGroup.querySelector(".input-help");
      helpText.innerHTML =
        '<i class="bi bi-info-circle"></i> This name will be validated and created when you submit the event';
      helpText.className = "input-help";
    }
  }

  async checkLocationNameUniqueness(name) {
    if (!name || name.length < 2) return;

    try {
      // Add loading indicator
      const helpText =
        this.elements.newLocationNameGroup.querySelector(".input-help");
      helpText.innerHTML =
        '<i class="bi bi-hourglass-split text-info"></i> Checking name availability...';
      helpText.className = "input-help text-info";

      // Check if location name already exists
      const response = await fetch(
        `/location/search?query=${encodeURIComponent(name)}`
      );
      if (!response.ok) throw new Error("Failed to check location names");

      const existingLocations = await response.json();
      const nameExists = existingLocations.some(
        (loc) => loc.name.toLowerCase() === name.toLowerCase()
      );

      if (nameExists) {
        // Name already exists
        this.elements.locationInput.value = "";
        this.elements.latitudeInput.value = "";
        this.elements.longitudeInput.value = "";

        helpText.innerHTML =
          '<i class="bi bi-exclamation-triangle text-danger"></i> This location name already exists. Please choose a different name.';
        helpText.className = "input-help text-danger";

        // Add visual feedback to input
        this.elements.newLocationName.classList.add("is-invalid");
      } else {
        // Name is available - populate form fields
        this.elements.locationInput.value = name;
        this.elements.latitudeInput.value = this.tempCoordinates.lat;
        this.elements.longitudeInput.value = this.tempCoordinates.lng;

        helpText.innerHTML =
          '<i class="bi bi-check-circle text-success"></i> Location name is available! Ready to create event.';
        helpText.className = "input-help text-success";

        // Remove invalid styling
        this.elements.newLocationName.classList.remove("is-invalid");
        this.elements.newLocationName.classList.add("is-valid");
      }
    } catch (error) {
      console.error("Error checking location name:", error);
      const helpText =
        this.elements.newLocationNameGroup.querySelector(".input-help");
      helpText.innerHTML =
        '<i class="bi bi-exclamation-triangle text-warning"></i> Could not verify name availability. Will check when creating event.';
      helpText.className = "input-help text-warning";
    }
  }

  extractLocationName(displayName) {
    const parts = displayName.split(",");
    let cleanName = parts[0].trim();

    // If first part is just a number, combine with second part
    if (/^\d+$/.test(cleanName) && parts.length > 1) {
      cleanName = `${cleanName} ${parts[1].trim()}`;
    }

    return cleanName;
  }

  setState(newState) {
    console.log(`LocationSelector: ${this.state} -> ${newState}`);
    this.state = newState;
  }

  showLoading(element) {
    element.classList.add("loading");
  }

  hideLoading(element) {
    element.classList.remove("loading");
  }

  showError(message) {
    // Use flash message system if available
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage(message, "danger");
    } else {
      alert(message);
    }
  }

  showSuccess(message) {
    // Use flash message system if available
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage(message, "success");
    } else {
      alert(message);
    }
  }

  handleClickOutside(e) {
    // Hide map suggestions if clicking outside
    if (!this.elements.mapSearchGroup.contains(e.target)) {
      this.elements.mapSuggestions.style.display = "none";
    }
  }

  hideSearchSection() {
    // Hide the search section and results
    this.elements.searchResults.style.display = "none";

    // Clear search input for next use
    this.elements.locationSearch.value = "";

    // Reset search button state
    this.showSearchLoading(false);
  }

  showSearchSection() {
    // Show search section and focus on input
    this.elements.locationSearch.focus();

    // Clear any existing search results
    this.elements.searchResults.style.display = "none";
    this.elements.resultsList.innerHTML = "";

    // Ensure search button is ready
    this.showSearchLoading(false);
  }

  hideNewLocationCreation() {
    this.elements.mapSearchGroup.style.display = "none";
    this.elements.newLocationNameGroup.style.display = "none";
    this.elements.mapSection.style.display = "none";

    if (this.map) {
      this.map.remove();
      this.map = null;
    }
  }

  changeLocation() {
    // Reset all location-related state
    this.selectedLocation = null;
    this.tempCoordinates = null;

    // Hide all location display sections
    this.elements.selectedSection.style.display = "none";
    this.elements.mapSection.style.display = "none";
    this.elements.mapSearchGroup.style.display = "none";
    this.elements.coordinatesStatus.style.display = "none";
    this.elements.newLocationNameGroup.style.display = "none";

    // Clear form inputs
    this.elements.locationInput.value = "";
    this.elements.latitudeInput.value = "";
    this.elements.longitudeInput.value = "";

    // Reset map search placeholder
    this.elements.mapSearch.placeholder = "Search for address...";
    this.elements.mapSearch.value = "";

    // Clean up map if it exists
    if (this.map) {
      this.map.remove();
      this.map = null;
    }

    // Show search section and focus on input
    this.showSearchSection();

    // Return to initial state
    this.setState("INITIAL");
  }

  editMapLocation() {
    if (this.selectedLocation?.type === "existing") {
      // Create a new location based on the existing one, but user will provide new name and coordinates
      // This is the same as selecting a "New Location" from search results

      // Initialize temp coordinates with current location as starting point
      this.tempCoordinates = {
        lat: this.selectedLocation.lat,
        lng: this.selectedLocation.lng,
        mapDisplayName: `New location based on ${this.selectedLocation.name}`,
      };

      // Suggest a name based on the current location
      const suggestedName = `${this.selectedLocation.name} (Custom)`;

      // Show new location creation interface (same as selecting new location from search)
      this.elements.newLocationName.value = suggestedName;
      this.initializeMapForNewLocation();
      this.hideSearchSection();
      this.setState("CREATING_NEW");

      // Enhanced UX: Auto-focus on map search field and prepopulate with current location
      setTimeout(() => {
        // Prepopulate the map search field with the current location name
        if (this.elements.mapSearch) {
          this.elements.mapSearch.value = this.selectedLocation.name;
          console.log(
            `✨ Auto-populated map search field with current location: ${this.selectedLocation.name}`
          );

          // Auto-focus on the map search field for immediate editing
          this.elements.mapSearch.focus();
          this.elements.mapSearch.select(); // Select all text for easy replacement
          console.log("Auto-focused on map search field for better UX");
        }

        // Auto-scroll to the map search section
        if (this.elements.mapSearchGroup) {
          this.elements.mapSearchGroup.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          console.log("Auto-scrolled to map search section");
        }

        // Validate the form since we now have coordinates
        this.validateNewLocationForm();
      }, 300); // Small delay to ensure map and UI are initialized
    }
  }

  debounceNameValidation() {
    clearTimeout(this.nameValidationTimeout);
    this.nameValidationTimeout = setTimeout(
      () => this.validateNewLocationForm(),
      500
    );
  }

  async reverseGeocode(lat, lng) {
    try {
      // Show loading indicator in search field
      const originalPlaceholder = this.elements.mapSearch.placeholder;
      this.elements.mapSearch.placeholder = "Loading address...";
      this.elements.mapSearch.disabled = true;

      // Call Nominatim reverse geocoding API
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`
      );

      if (!response.ok) {
        throw new Error("Reverse geocoding failed");
      }

      const data = await response.json();

      if (data && data.display_name) {
        // Update the search field with the new address
        this.elements.mapSearch.value = data.display_name;

        // Extract a clean name for the location
        const cleanName = this.extractLocationName(data.display_name);

        // Update the new location name field if it's empty or contains the old suggested name
        const currentName = this.elements.newLocationName.value.trim();
        if (
          !currentName ||
          currentName.includes("(Custom)") ||
          currentName === this.lastSuggestedName
        ) {
          this.elements.newLocationName.value = cleanName;
          this.lastSuggestedName = cleanName;

          // Trigger validation for the new name
          this.validateNewLocationForm();
        }

        console.log(`Reverse geocoded coordinates to: ${data.display_name}`);
      }
    } catch (error) {
      console.error("Reverse geocoding error:", error);
      // Don't show error to user, just log it
    } finally {
      // Reset search field state
      this.elements.mapSearch.placeholder = "Search for address...";
      this.elements.mapSearch.disabled = false;
    }
  }
}

/* ===== JOURNEY DETAIL FUNCTIONS ===== */

// Smart back button functionality
function smartBack() {
  // Get page data for session info
  const pageData = document.getElementById("pageData");
  const journeyPage = pageData?.dataset.journeyPage;
  const isLoggedIn = pageData?.dataset.isLoggedIn === "true";

  // First check session data - this persists through page reloads
  if (journeyPage === "departure_board") {
    window.location.href = "/departure_board";
    return;
  } else if (journeyPage === "published") {
    // Handle published journey navigation
    const referrer = document.referrer;

    // If came from published journey list, go back there
    if (referrer && referrer.includes("/published_journey")) {
      window.location.href =
        pageData?.dataset.publishedListUrl || "/published_journey";
      return;
    }

    // If came from landing page or no referrer, go to landing page for anonymous users
    if (
      !isLoggedIn ||
      (referrer && referrer.includes(window.location.origin + "/"))
    ) {
      window.location.href = pageData?.dataset.landingUrl || "/";
      return;
    }

    // For logged-in users coming from other pages, go to published journey list
    window.location.href =
      pageData?.dataset.publishedListUrl || "/published_journey";
    return;
  } else if (journeyPage === "notifications") {
    // Coming from notifications page, go back to it
    window.location.href = "/notifications/all";
    return;
  }

  // Then check referrer as fallback
  const referrer = document.referrer;
  if (referrer && referrer.includes("/departure_board")) {
    window.location.href = "/departure_board";
    return;
  } else if (referrer && referrer.includes("/discovery")) {
    window.location.href = "/discovery";
    return;
  } else if (referrer && referrer.includes("/dashboard")) {
    window.location.href = "/dashboard";
    return;
  }

  // For journey/event pages, try to be smart about context
  const currentPath = window.location.pathname;

  if (currentPath.includes("/event/")) {
    // For events, try to go back to the journey
    if (pageData) {
      const journeyId = pageData.dataset.journeyId;
      if (journeyId) {
        window.location.href = `/journey/public/${journeyId}`;
        return;
      }
    }
    window.location.href = "/journey/public";
  } else if (currentPath.includes("/journey/private/")) {
    window.location.href = "/journey/private";
  } else if (currentPath.includes("/journey/published/")) {
    // Published journey detail
    if (!isLoggedIn) {
      window.location.href = "/";
    } else {
      window.location.href = "/published_journey";
    }
  } else {
    window.location.href = "/journey/public";
  }
}

// Update back button text based on context
function updateBackButtonText() {
  const backButtonText = document.getElementById("backButtonText");
  if (!backButtonText) return;

  // Get page data for session info
  const pageData = document.getElementById("pageData");
  const journeyPage = pageData?.dataset.journeyPage;
  const isLoggedIn = pageData?.dataset.isLoggedIn === "true";

  // Check session data first (most reliable)
  if (journeyPage === "departure_board") {
    backButtonText.textContent = "Back to Departure Board";
    return;
  } else if (journeyPage === "published") {
    // Handle published journey navigation text
    const referrer = document.referrer;

    // If came from published journey list
    if (referrer && referrer.includes("/published_journey")) {
      backButtonText.textContent = "Back to Published Journeys";
      return;
    }

    // If came from landing page or no referrer, for anonymous users
    if (
      !isLoggedIn ||
      (referrer && referrer.includes(window.location.origin + "/"))
    ) {
      backButtonText.textContent = "Back to Home";
      return;
    }

    // For logged-in users coming from other pages
    backButtonText.textContent = "Back to Published Journeys";
    return;
  } else if (journeyPage === "notifications") {
    // Coming from notifications page
    backButtonText.textContent = "Back to Notifications";
    return;
  }

  // Check referrer to determine back button text
  const referrer = document.referrer;

  if (referrer && referrer.includes("/departure_board")) {
    backButtonText.textContent = "Back to Departure Board";
    return;
  } else if (referrer && referrer.includes("/discovery")) {
    backButtonText.textContent = "Back to Discovery";
    return;
  } else if (referrer && referrer.includes("/dashboard")) {
    backButtonText.textContent = "Back to Dashboard";
    return;
  }

  // Context-based text based on current page
  const currentPath = window.location.pathname;

  if (currentPath.includes("/journey/public/")) {
    backButtonText.textContent = "Back to Public Journeys";
  } else if (currentPath.includes("/journey/private/")) {
    backButtonText.textContent = "Back to My Journeys";
  } else if (currentPath.includes("/journey/published/")) {
    if (!isLoggedIn) {
      backButtonText.textContent = "Back to Home";
    } else {
      backButtonText.textContent = "Back to Published Journeys";
    }
  } else if (currentPath.includes("/event/")) {
    backButtonText.textContent = "Back to Journey";
  } else if (currentPath.includes("/edit-history/")) {
    backButtonText.textContent = "Back to Journey";
  } else {
    backButtonText.textContent = "Back";
  }
}

// Show protected journey message
function showProtectedJourneyMessage() {
  const modalContent = `
    <div class="alert alert-info border-0 rounded-3">
      <div class="d-flex align-items-start">
        <i class="bi bi-info-circle me-2 mt-1"></i>
        <div>
          <strong>Note:</strong> This protection was enabled by the journey owner to maintain content integrity.
          However, as a staff member, you can still hide the journey if needed for moderation purposes.
        </div>
      </div>
    </div>
  `;

  // Check if showModal function is available (from modal.html)
  if (typeof window.showModal === "function") {
    window.showModal("Journey Protection Information", modalContent, {
      hideActionButton: true,
      modalSize: "medium",
    });
  } else {
    // Fallback to alert if modal system is not available
    alert("This journey is protected from editing by staff members.");
  }
}

// Cover image modal functionality
function showCoverImageModal(imageUrl, journeyTitle) {
  const modalContent = `
    <div style="text-align:center;">
      <img 
        src="${imageUrl}" 
        alt="Cover image for ${journeyTitle}"
        style="
          max-width: 100%;
          max-height: 90vh;
          object-fit: contain;
          display: block;
          margin: 0 auto;
          border-radius: 8px;
          box-shadow: 0 2px 16px rgba(0,0,0,0.08);
        "
      >
    </div>
  `;
  if (typeof window.showModal === "function") {
    window.showModal("", modalContent, {
      hideActionButton: true,
      modalSize: "large",
    });
  }
}

// Setup cover image management
function setupCoverImageManagement() {
  console.log("Setting up cover image management...");

  const addBtn = document.getElementById("addCoverImageBtn");
  const changeBtn = document.getElementById("changeCoverImageBtn");
  const removeBtn = document.getElementById("removeCoverImageBtn");
  const fileInput = document.getElementById("coverImageInput");

  console.log("Cover image elements found:", {
    addBtn: !!addBtn,
    changeBtn: !!changeBtn,
    removeBtn: !!removeBtn,
    fileInput: !!fileInput,
  });

  // Add cover image
  if (addBtn && fileInput) {
    addBtn.addEventListener("click", function () {
      console.log("Add cover image button clicked");
      fileInput.click();
    });
  }

  // Change cover image
  if (changeBtn && fileInput) {
    changeBtn.addEventListener("click", function () {
      console.log("Change cover image button clicked");
      fileInput.click();
    });
  }

  // Remove cover image
  if (removeBtn) {
    removeBtn.addEventListener("click", function (e) {
      console.log("Remove cover image button clicked");

      // Check if button is protected (for journeys with no_edits)
      if (removeBtn.dataset.protected === "true") {
        e.preventDefault();
        console.log(
          "🖼️ Remove button is protected, showing protected journey message"
        );
        showProtectedJourneyMessage();
        return;
      }

      // If button is disabled for other reasons, show protected journey message
      if (removeBtn.disabled || removeBtn.classList.contains("disabled")) {
        e.preventDefault();
        console.log(
          "🖼️ Remove button is disabled, showing protected journey message"
        );
        showProtectedJourneyMessage();
        return;
      }

      handleRemoveCoverImage();
    });
  }

  // Handle file input change
  if (fileInput) {
    fileInput.addEventListener("change", function (e) {
      if (e.target.files && e.target.files[0]) {
        console.log("File selected for upload:", e.target.files[0].name);
        uploadCoverImage(e.target.files[0]);
      }
    });
  }
}

// Upload cover image
async function uploadCoverImage(file) {
  if (!file) {
    console.error("File is required");
    return;
  }

  // Get upload URL from page data
  const pageData = document.getElementById("pageData");
  const uploadUrl = pageData?.dataset.uploadUrl;

  if (!uploadUrl) {
    console.error("Upload URL not found in page data");
    return;
  }

  // Validate file size (5MB limit)
  if (file.size > 5 * 1024 * 1024) {
    showFlashMessage(
      "File size exceeds 5MB limit. Please choose a smaller file.",
      "danger"
    );
    return;
  }

  // Validate file type
  const allowedTypes = ["image/png", "image/jpg", "image/jpeg", "image/gif"];
  if (!allowedTypes.includes(file.type)) {
    showFlashMessage(
      "Invalid file type. Please choose a PNG, JPG, JPEG, or GIF file.",
      "danger"
    );
    return;
  }

  try {
    const formData = new FormData();
    formData.append("image", file);

    const response = await fetch(uploadUrl, {
      method: "POST",
      body: formData,
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    const data = await response.json();

    // Update DOM instead of reloading page
    if (data.success) {
      if (data.image_url) {
        updateCoverImageDOM(data.image_url);
        showFlashMessage(
          data.message || "Cover image uploaded successfully",
          "success"
        );
      } else {
        // Success but no image URL provided - reload to get the updated image
        console.warn(
          "⚠️ Upload successful but no image URL provided, reloading page"
        );
        showFlashMessage(
          data.message || "Cover image uploaded successfully",
          "success"
        );
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    } else {
      showFlashMessage(
        data.message || "Failed to upload cover image",
        "danger"
      );
    }
  } catch (error) {
    console.error("Error uploading cover image:", error);
    showFlashMessage(
      "Failed to upload cover image. Please try again.",
      "danger"
    );
  } finally {
    // Close modal
    if (typeof window.closeModal === "function") {
      window.closeModal();
    }
  }
}

// Show protected journey message
function showProtectedJourneyMessage() {
  if (typeof window.showModal === "function") {
    window.showModal(
      "Protected Journey",
      `
        <div class="text-center">
          <i class="bi bi-shield-check text-warning fs-1 mb-3"></i>
          <h5>This journey is protected from editing</h5>
          <p class="text-muted mb-3">
            The journey owner has enabled "No Edits" protection, which prevents content managers from making changes to this content.
          </p>
          <div class="alert alert-info d-flex align-items-start" role="alert">
            <i class="bi bi-info-circle-fill me-2 mt-1"></i>
            <div class="text-start">
              <strong>Note:</strong> You can still hide this journey if the content is inappropriate or violates community guidelines.
            </div>
          </div>
        </div>
      `,
      {
        actionText: "Understood",
        onAction: () => true,
      }
    );
  } else {
    showFlashMessage(
      "This journey is protected from content manager edits. You can still hide inappropriate content.",
      "warning"
    );
  }
}

// Handle remove cover image
async function handleRemoveCoverImage() {
  // Get remove URL from page data
  const pageData = document.getElementById("pageData");
  const removeUrl = pageData?.dataset.removeUrl;

  if (!removeUrl) {
    console.error("Remove URL not found in page data");
    return;
  }

  // Get user permissions
  const journeyUserId = parseInt(pageData?.dataset.journeyUserId);
  const sessionUserId = parseInt(pageData?.dataset.sessionUserId);
  const isOwner = journeyUserId === sessionUserId;
  const canManageContent = pageData?.dataset.canManageContent === "true";
  const noEdits = pageData?.dataset.noEdits === "true";

  // Check if journey is protected from content management edits
  if (canManageContent && !isOwner && noEdits) {
    showProtectedJourneyMessage();
    return;
  }

  // Check if button is disabled (additional safety check)
  const removeButton = document.getElementById("removeCoverImageBtn");
  if (removeButton && removeButton.disabled) {
    showProtectedJourneyMessage();
    return;
  }

  // If content manager is removing someone else's cover image, require edit reason
  if (canManageContent && !isOwner) {
    const editReasonForm = `
      <div class="mb-3">
        <label for="editReason" class="form-label">Reason for Removal *</label>
        <textarea class="form-control" id="editReason" rows="3" required
          placeholder="Please provide a reason for removing this cover image..."></textarea>
        <div class="invalid-feedback">Content managers must provide a reason for removing user content</div>
      </div>
      <p class="text-muted small">As a content manager, you must provide a reason when removing user content.</p>
    `;

    if (typeof window.showModal === "function") {
      window.showModal("Remove Cover Image", editReasonForm, {
        actionText: "Remove",
        onAction: async function () {
          const editReason = document.getElementById("editReason").value.trim();
          if (!editReason) {
            document.getElementById("editReason").classList.add("is-invalid");
            return false; // Prevent modal from closing
          }

          try {
            const formData = new FormData();
            formData.append("edit_reason", editReason);

            const response = await fetch(removeUrl, {
              method: "POST",
              body: formData,
              headers: {
                "X-Requested-With": "XMLHttpRequest",
              },
            });

            const data = await response.json();

            if (data.success !== false) {
              updateCoverImageDOM(null);
              showFlashMessage(
                data.message || "Cover image removed successfully",
                "success"
              );
              return true;
            } else {
              showFlashMessage(
                data.message || "Failed to remove cover image",
                "danger"
              );
              return false;
            }
          } catch (error) {
            console.error("Error removing cover image:", error);
            showFlashMessage(
              "Failed to remove cover image. Please try again.",
              "danger"
            );
            return false;
          }
        },
      });
    }
  } else {
    // Owner removing their own cover image - no edit reason needed
    if (typeof window.showModal === "function") {
      window.showModal(
        "Remove Cover Image",
        "Are you sure you want to remove this cover image? This action cannot be undone.",
        {
          actionText: "Remove",
          onAction: async function () {
            const success = await performRemoveCoverImage(removeUrl);
            return success;
          },
        }
      );
    }
  }
}

// Perform remove cover image
async function performRemoveCoverImage(removeUrl) {
  try {
    const response = await fetch(removeUrl, {
      method: "POST",
      headers: {
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    // Check if response is JSON
    const contentType = response.headers.get("content-type");
    if (!contentType || !contentType.includes("application/json")) {
      const text = await response.text();
      console.error("Server returned non-JSON response:", text);
      throw new Error("Server returned an error page instead of JSON response");
    }

    const data = await response.json();

    // Update DOM and show message based on actual response
    if (data.success !== false) {
      updateCoverImageDOM(null);
      showFlashMessage(
        data.message || "Cover image removed successfully",
        "success"
      );
      return true; // Indicate success to modal handler
    } else {
      showFlashMessage(
        data.message || "Failed to remove cover image",
        "danger"
      );
      return false; // Indicate failure to modal handler
    }
  } catch (error) {
    console.error("Error removing cover image:", error);
    showFlashMessage(
      "Failed to remove cover image. Please try again.",
      "danger"
    );
    return false; // Indicate failure to modal handler
  }
}

// Update cover image DOM
function updateCoverImageDOM(imageUrl) {
  console.log("Updating cover image DOM, imageUrl:", imageUrl);

  const coverImageContainer = document.querySelector(".cover-image");
  const coverImagePlaceholder = document.querySelector(
    ".cover-image-placeholder"
  );

  console.log("Found elements:", {
    coverImageContainer: !!coverImageContainer,
    coverImagePlaceholder: !!coverImagePlaceholder,
  });

  if (!coverImageContainer && !coverImagePlaceholder) {
    console.error("No cover image elements found!");
    return;
  }

  // Get user permissions
  const pageData = document.getElementById("pageData");
  const canEdit = pageData?.dataset.canEdit === "true";
  const journeyUserId = parseInt(pageData?.dataset.journeyUserId);
  const sessionUserId = parseInt(pageData?.dataset.sessionUserId);
  const isOwner = journeyUserId === sessionUserId;
  const journeyTitle = pageData?.dataset.journeyTitle || "Journey";

  if (imageUrl) {
    // Hide placeholder if it exists
    if (coverImagePlaceholder) {
      coverImagePlaceholder.style.display = "none";
      coverImagePlaceholder.classList.add("d-none");
    }

    // Create or update image container
    let imageContainer = coverImageContainer;
    if (!imageContainer) {
      imageContainer = document.createElement("div");
      imageContainer.className = "cover-image position-relative";
      imageContainer.id = "coverImageContainer";
      const parent =
        coverImagePlaceholder?.parentElement ||
        document.querySelector(".cover-image-container");
      if (parent) {
        parent.appendChild(imageContainer);
      }
    }

    // Update with new image including controls
    imageContainer.innerHTML = `
      <div class="cover-image-clickable"
        data-image-url="${imageUrl}"
        data-title="${journeyTitle}"
        onclick="showCoverImageModal(this.dataset.imageUrl, this.dataset.title)">
        <img src="${imageUrl}" alt="Cover image" class="cover-image-img" id="coverImageImg">
        <div class="cover-image-overlay">
          <i class="bi bi-zoom-in"></i>
          <span>Click to enlarge</span>
        </div>
      </div>
      ${
        canEdit
          ? `
        <div class="cover-image-controls position-absolute bottom-0 end-0 m-2">
          ${
            isOwner
              ? `
            <button class="btn btn-sm btn-light rounded-pill me-2" id="changeCoverImageBtn">
              <i class="bi bi-image me-1"></i> Change
            </button>
          `
              : ""
          }
          <button class="btn btn-sm btn-light rounded-pill" id="removeCoverImageBtn">
            <i class="bi bi-trash me-1"></i> Remove
          </button>
        </div>
      `
          : ""
      }
    `;

    imageContainer.style.display = "block";
    imageContainer.classList.remove("d-none");
  } else {
    // Hide image container if it exists
    if (coverImageContainer) {
      coverImageContainer.style.display = "none";
      coverImageContainer.classList.add("d-none");
    }

    // Show placeholder
    let placeholder = coverImagePlaceholder;

    // If placeholder doesn't exist, create it
    if (!placeholder) {
      console.log("Creating placeholder element");
      placeholder = document.createElement("div");
      placeholder.className =
        "cover-image cover-image-placeholder d-flex align-items-center justify-content-center";
      placeholder.id = "coverImagePlaceholder";

      // Find parent container and insert placeholder
      const parent =
        coverImageContainer?.parentElement ||
        document.querySelector(".cover-image-container") ||
        document.querySelector(".card-body");

      if (parent) {
        if (coverImageContainer) {
          parent.insertBefore(placeholder, coverImageContainer);
        } else {
          parent.appendChild(placeholder);
        }
      }
    }

    if (placeholder) {
      console.log("Showing placeholder");
      placeholder.style.display = "flex";
      placeholder.classList.remove("d-none");

      // Get premium access from page data
      const premiumAccess = pageData?.dataset.premiumAccess === "true";

      // Update placeholder content with add button if owner
      placeholder.innerHTML = `
        <div class="text-center">
          ${
            isOwner
              ? premiumAccess
                ? `
            <button class="btn btn-light btn-sm rounded-pill" id="addCoverImageBtn">
              <i class="bi bi-image me-2"></i> Add Cover Image
            </button>
          `
                : `
            <div class="text-muted small mb-2">Cover images are available for premium users</div>
            <a href="/account/profile?active_tab=subscription" class="btn btn-primary btn-sm rounded-pill">
              <i class="bi bi-star me-1"></i> Upgrade to Premium
            </a>
          `
              : `
            <div class="text-muted">No cover image</div>
          `
          }
        </div>
      `;
    } else {
      console.error("Failed to create or find placeholder element");
    }
  }

  // Re-attach event listeners to new buttons
  setTimeout(() => {
    setupCoverImageManagement();
  }, 100);
}

// Attach cover image button listeners
function attachCoverImageButtonListeners() {
  const coverImage = document.querySelector(".cover-image img");
  if (coverImage) {
    coverImage.style.cursor = "pointer";
    coverImage.addEventListener("click", () => {
      const imageUrl = coverImage.src;
      const journeyTitle =
        document.getElementById("pageData")?.dataset.journeyTitle || "Journey";
      showCoverImageModal(imageUrl, journeyTitle);
    });
  }
}

// Get locations data from the page
function getLocationsData() {
  try {
    const locationsScript = document.getElementById("locationsData");
    if (locationsScript && locationsScript.textContent) {
      return JSON.parse(locationsScript.textContent);
    }
    return [];
  } catch (error) {
    console.error("Error parsing locations data:", error);
    return [];
  }
}

/* ===== MAP OPERATIONS ===== */

// Global map instances
let journeyMap = null;
let eventMaps = {};

/**
 * Create a red icon for markers
 * @returns {L.Icon} Red marker icon
 */
function createRedIcon() {
  return L.icon({
    iconUrl:
      "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png",
    shadowUrl:
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  });
}

/**
 * Create a blue icon for markers
 * @returns {L.Icon} Blue marker icon
 */
function createBlueIcon() {
  return L.icon({
    iconUrl:
      "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-blue.png",
    shadowUrl:
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  });
}

/**
 * Initialize the main journey map
 */
function initializeJourneyMap() {
  console.log("Initializing journey map...");

  const mapContainer = document.getElementById("journeyMap");
  if (!mapContainer) {
    console.log("Journey map container not found, skipping map initialization");
    return;
  }

  // Check if Leaflet is loaded
  if (typeof L === "undefined") {
    console.log("Leaflet not loaded, skipping map initialization");
    return;
  }

  try {
    const locations = getLocationsData();
    console.log("Locations data:", locations);

    if (!locations || locations.length === 0) {
      console.log(
        "No locations found for journey map - container should not exist"
      );
      return;
    }

    // Calculate bounds for all locations
    const validLocations = locations.filter(
      (loc) => loc.latitude && loc.longitude
    );

    if (validLocations.length === 0) {
      console.log("No valid coordinates found for journey map");
      return;
    }

    // Use first location as center, or calculate center of all locations
    let centerLat, centerLng, zoom;

    if (validLocations.length === 1) {
      centerLat = parseFloat(validLocations[0].latitude);
      centerLng = parseFloat(validLocations[0].longitude);
      zoom = 13;
    } else {
      // Calculate bounds
      const lats = validLocations.map((loc) => parseFloat(loc.latitude));
      const lngs = validLocations.map((loc) => parseFloat(loc.longitude));

      centerLat = (Math.min(...lats) + Math.max(...lats)) / 2;
      centerLng = (Math.min(...lngs) + Math.max(...lngs)) / 2;
      zoom = 10; // Zoom out to show multiple locations
    }

    // Create map directly
    journeyMap = L.map("journeyMap").setView([centerLat, centerLng], zoom);
    L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png", {
      maxZoom: 19,
      attribution:
        '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
    }).addTo(journeyMap);

    if (!journeyMap) {
      console.error("Failed to create journey map");
      return;
    }

    // Sort locations by earliest event date to create journey path
    const sortedLocations = [...validLocations].sort((a, b) => {
      const aDate =
        a.events && a.events.length > 0
          ? Math.min(
              ...a.events.map((e) => new Date(e.start_datetime).getTime())
            )
          : Infinity;
      const bDate =
        b.events && b.events.length > 0
          ? Math.min(
              ...b.events.map((e) => new Date(e.start_datetime).getTime())
            )
          : Infinity;
      return aDate - bDate;
    });

    // Create path coordinates for the journey line
    const pathCoordinates = sortedLocations.map((location) => [
      parseFloat(location.latitude),
      parseFloat(location.longitude),
    ]);

    // Add journey path line if there are multiple locations
    if (pathCoordinates.length > 1) {
      L.polyline(pathCoordinates, {
        color: "#dc3545",
        weight: 3,
        opacity: 0.7,
        smoothFactor: 1,
      }).addTo(journeyMap);
    }

    // Add markers for each location (using sorted order for numbering)
    sortedLocations.forEach((location, index) => {
      const lat = parseFloat(location.latitude);
      const lng = parseFloat(location.longitude);

      if (isNaN(lat) || isNaN(lng)) {
        console.warn(
          `Invalid coordinates for location ${location.id}:`,
          location
        );
        return;
      }

      // All locations use red icons (consistent with original implementation)
      const icon = createRedIcon();
      const marker = L.marker([lat, lng], { icon }).addTo(journeyMap);
    });

    // Fit map to show all markers if multiple locations
    if (validLocations.length > 1) {
      const group = new L.featureGroup(
        validLocations.map((loc) =>
          L.marker([parseFloat(loc.latitude), parseFloat(loc.longitude)])
        )
      );
      journeyMap.fitBounds(group.getBounds().pad(0.1));
    }

    console.log("Journey map initialized successfully");
  } catch (error) {
    console.error("Error initializing journey map:", error);
    mapContainer.innerHTML = `
      <div class="d-flex align-items-center justify-content-center h-100 text-muted">
        <div class="text-center">
          <i class="bi bi-exclamation-triangle fs-1 mb-2"></i>
          <p class="mb-0">Error loading map</p>
        </div>
      </div>
    `;
  }
}

/**
 * Toggle event map visibility
 * @param {number} eventId - Event ID
 */
function toggleEventMap(eventId) {
  const mapContainer = document.getElementById(`inlineMap${eventId}`);
  const toggleButton = document.querySelector(
    `[data-event-id="${eventId}"][data-action="toggle-map"]`
  );

  if (!mapContainer || !toggleButton) {
    console.error(
      `Map container or toggle button not found for event ${eventId}`
    );
    return;
  }

  const isVisible = mapContainer.style.display !== "none";

  if (isVisible) {
    // Hide map
    mapContainer.style.display = "none";
    toggleButton.innerHTML = '<i class="bi bi-geo-alt"></i> Show Location';

    // Clean up map instance
    if (eventMaps[eventId]) {
      eventMaps[eventId].remove();
      delete eventMaps[eventId];
    }
  } else {
    // Show map
    mapContainer.style.display = "block";
    toggleButton.innerHTML = '<i class="bi bi-geo-alt-fill"></i> Hide Location';

    // Initialize map if not already done
    initializeEventMap(eventId);
  }
}

/**
 * Initialize an individual event map
 * @param {number} eventId - Event ID
 */
function initializeEventMap(eventId) {
  const mapContainer = document.getElementById(`inlineMap${eventId}`);
  if (!mapContainer) {
    console.error(`Map container not found for event ${eventId}`);
    return;
  }

  const latitude = mapContainer.dataset.latitude;
  const longitude = mapContainer.dataset.longitude;
  const locationName = mapContainer.dataset.locationName;

  if (!latitude || !longitude) {
    console.error(`Missing coordinates for event ${eventId}`);
    return;
  }

  try {
    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);

    if (isNaN(lat) || isNaN(lng)) {
      console.error(
        `Invalid coordinates for event ${eventId}: lat=${latitude}, lng=${longitude}`
      );
      return;
    }

    const map = L.map(`inlineMap${eventId}`).setView([lat, lng], 15);
    L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png", {
      maxZoom: 19,
      attribution:
        '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
    }).addTo(map);

    const redIcon = createRedIcon();
    L.marker([lat, lng], { icon: redIcon })
      .addTo(map)
      .bindPopup(
        `<div class="location-popup"><h6>${
          locationName || "Location"
        }</h6></div>`
      )
      .openPopup();

    eventMaps[eventId] = map;
  } catch (error) {
    console.error(`Error initializing event map ${eventId}:`, error);
  }
}

/* ===== EDIT HISTORY ===== */

/**
 * Open edit history modal for a journey
 * @param {number} journeyId - Journey ID
 * @param {string} journeyTitle - Journey title for display
 */
function openEditHistoryModal(journeyId, journeyTitle) {
  if (!journeyId) {
    console.error("Journey ID is required");
    return;
  }

  console.log(
    `Opening edit history modal for journey ${journeyId}: ${journeyTitle}`
  );

  // Check if showModal function is available
  if (typeof showModal !== "function") {
    console.error(
      "showModal function not available! Cannot open edit history modal."
    );
    return;
  }

  // Show loading state using common modal
  const loadingContent = `
    <div class="edit-history-loading">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p>Loading edit history...</p>
    </div>
  `;

  showModal(`Edit History`, loadingContent);

  // Fetch edit history modal content (server-side rendered template)
  fetch(`/edit-history/modal/journey/${journeyId}`)
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.text();
    })
    .then((html) => {
      // Update modal with the server-rendered content using common modal
      showModal(`Edit History`, html);
    })
    .catch((error) => {
      console.error("Error fetching edit history modal:", error);

      // Show error using common modal
      const errorContent = `
        <div class="edit-history-empty">
          <i class="bi bi-exclamation-triangle"></i>
          <h4>Error Loading History</h4>
          <p>Failed to load edit history. Please try again.</p>
        </div>
      `;

      showModal(`Edit History`, errorContent);
    });
}

/* ===== INITIALIZATION ===== */

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  console.log("Journey Detail Bundle loaded - DOM ready");

  // Check flash messages availability (should be available since bundle loads after base.html)
  if (typeof showFlashMessage === "undefined") {
    console.warn(
      "⚠️ Flash messages not available - some features may not work properly"
    );
  } else {
    console.log("Flash messages available from base.html");
  }

  // Initialize lazy loading
  LazyLoader.initialize();

  // Initialize location operations
  if (window.LocationOperations) {
    window.LocationOperations.initialize();
  }

  // Initialize journey operations
  console.log("Creating JourneyOperations instance...");
  window.journeyOperations = new JourneyOperations();
  console.log("JourneyOperations instance created:", window.journeyOperations);

  // Initialize journey detail functions
  updateBackButtonText();

  // Initialize journey map
  initializeJourneyMap();

  // Setup cover image management if user can edit
  const pageData = document.getElementById("pageData");
  if (pageData && pageData.dataset.canEdit === "true") {
    setupCoverImageManagement();
    attachCoverImageButtonListeners();
  }

  // Make functions globally available (excluding flash message functions - they're from base.html)
  window.EnhancedFormValidation = EnhancedFormValidation;
  window.ImagePreview = ImagePreview;
  window.FileValidation = FileValidation;
  window.LazyLoader = LazyLoader;
  window.LocationOperations = LocationOperations;
  window.smartBack = smartBack;
  window.updateBackButtonText = updateBackButtonText;
  window.showProtectedJourneyMessage = showProtectedJourneyMessage;
  window.showCoverImageModal = showCoverImageModal;
  window.setupCoverImageManagement = setupCoverImageManagement;
  window.uploadCoverImage = uploadCoverImage;
  window.handleRemoveCoverImage = handleRemoveCoverImage;
  window.performRemoveCoverImage = performRemoveCoverImage;
  window.updateCoverImageDOM = updateCoverImageDOM;
  window.attachCoverImageButtonListeners = attachCoverImageButtonListeners;
  window.getLocationsData = getLocationsData;
  window.initializeJourneyMap = initializeJourneyMap;
  window.toggleEventMap = toggleEventMap;
  window.openEditHistoryModal = openEditHistoryModal;
  window.openEventLocationModal = openEventLocationModal;
  window.openJourneyMapModal = openJourneyMapModal;
  window.updateLocationFollowButton = updateLocationFollowButton;
  window.toggleLocationFollow = toggleLocationFollow;
  window.initializeFullEventMap = initializeFullEventMap;

  console.log("Journey Detail Bundle initialization complete");
});

// Add event listeners for location follow functionality
document.addEventListener("click", (e) => {
  const locationFollowButton = e.target.closest(
    '[data-action="toggle-location-follow"]'
  );
  if (locationFollowButton) {
    e.preventDefault();
    e.stopPropagation();
    const locationId = locationFollowButton.dataset.locationId;
    if (locationId) {
      toggleLocationFollow(locationId, locationFollowButton);
    }
  }
});

// Export for module usage (excluding flash message functions - they're from base.html)
if (typeof module !== "undefined" && module.exports) {
  module.exports = {
    EnhancedFormValidation,
    ImagePreview,
    FileValidation,
    LazyLoader,
    LocationOperations,
    JourneyOperations,
    smartBack,
    updateBackButtonText,
    showProtectedJourneyMessage,
    showCoverImageModal,
    initializeJourneyMap,
    toggleEventMap,
    openEditHistoryModal,
    openEventLocationModal,
    updateLocationFollowButton,
    toggleLocationFollow,
    initializeFullEventMap,
  };
}

/**
 * Create a red icon for markers
 * @returns {L.Icon} Red marker icon
 */
function createRedIcon() {
  return L.icon({
    iconUrl:
      "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png",
    shadowUrl:
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  });
}

/**
 * Create a blue icon for markers
 * @returns {L.Icon} Blue marker icon
 */
function createBlueIcon() {
  return L.icon({
    iconUrl:
      "https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-blue.png",
    shadowUrl:
      "https://cdnjs.cloudflare.com/ajax/libs/leaflet/0.7.7/images/marker-shadow.png",
    iconSize: [25, 41],
    iconAnchor: [12, 41],
    popupAnchor: [1, -34],
    shadowSize: [41, 41],
  });
}

/**
 * Open journey map modal
 */
function openJourneyMapModal() {
  const locations = getLocationsData();

  if (!locations || locations.length === 0) {
    showFlashMessage("No locations available to display on map", "info");
    return;
  }

  // Use Bootstrap modal directly instead of showModal
  const modal = new bootstrap.Modal(document.getElementById("fullMapModal"));
  modal.show();

  // Initialize map in modal after it's shown
  setTimeout(() => {
    initializeModalJourneyMap(locations);
  }, 300);
}

/**
 * Initialize modal journey map
 */
function initializeModalJourneyMap(locations) {
  const mapElement = document.getElementById("fullJourneyMap");
  if (!mapElement) {
    console.error("Full journey map element not found");
    return;
  }

  const locationMap = new Map();
  locations.forEach((loc) => {
    if (!loc.latitude || !loc.longitude) return;

    const locId = loc.id;
    if (!locationMap.has(locId)) {
      locationMap.set(locId, {
        id: loc.id,
        name: loc.name,
        latitude: loc.latitude,
        longitude: loc.longitude,
        events: [],
      });
    }

    locationMap.get(locId).events.push({
      id: loc.event_id,
      title: loc.event_title,
      description: loc.event_description,
      start_datetime: loc.event_start_datetime,
    });
  });

  const validLocations = Array.from(locationMap.values());

  if (validLocations.length === 0) {
    console.warn("No valid locations for modal map");
    return;
  }

  try {
    // Remove existing map if any
    if (window.fullJourneyMapInstance) {
      window.fullJourneyMapInstance.remove();
    }

    // Use first location as center
    const centerLat = parseFloat(validLocations[0].latitude);
    const centerLng = parseFloat(validLocations[0].longitude);

    window.fullJourneyMapInstance = L.map("fullJourneyMap").setView(
      [centerLat, centerLng],
      10
    );
    L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png", {
      maxZoom: 19,
      attribution:
        '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
    }).addTo(window.fullJourneyMapInstance);

    // Sort locations by earliest event date to create journey path
    const sortedLocations = [...validLocations].sort((a, b) => {
      const aDate =
        a.events && a.events.length > 0
          ? Math.min(
              ...a.events.map((e) => new Date(e.start_datetime).getTime())
            )
          : Infinity;
      const bDate =
        b.events && b.events.length > 0
          ? Math.min(
              ...b.events.map((e) => new Date(e.start_datetime).getTime())
            )
          : Infinity;
      return aDate - bDate;
    });

    // Create path coordinates for the journey line
    const pathCoordinates = sortedLocations.map((location) => [
      parseFloat(location.latitude),
      parseFloat(location.longitude),
    ]);

    // Add journey path line if there are multiple locations
    if (pathCoordinates.length > 1) {
      L.polyline(pathCoordinates, {
        color: "#dc3545",
        weight: 3,
        opacity: 0.7,
        smoothFactor: 1,
      }).addTo(window.fullJourneyMapInstance);
    }

    // Add markers for each location (using sorted order)
    sortedLocations.forEach((location, index) => {
      const lat = parseFloat(location.latitude);
      const lng = parseFloat(location.longitude);

      if (isNaN(lat) || isNaN(lng)) return;

      // All markers use red icons (consistent with original implementation)
      const icon = createRedIcon();
      const marker = L.marker([lat, lng], { icon }).addTo(
        window.fullJourneyMapInstance
      );
      const locsAtSamePlace = location.events || [];

      const popupContent = `
        <div class="location-popup">
          ${location.name ? `<h5>${location.name}</h5>` : ""}
          <div class="carousel-container">
          ${
            locsAtSamePlace.length > 1
              ? `
            <button class="carousel-button prev" onclick="moveSlide(this, -1)">&#10094;</button>
          `
              : ""
          }
            <div class="carousel-content">
              ${locsAtSamePlace
                .map(
                  (loc, i) => `
                <div class="carousel-slide" style="display: ${
                  i === 0 ? "block" : "none"
                };">
                  <h5 class="info-title">Event Title</h5>
                  <p>${loc.title}</p>
                  <h5 class="info-title">Description</h5>
                  <p style="max-width: 250px; ; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">${
                    loc.description || "No description provided."
                  }</p>
                  <h5 class="info-title">Started</h5>
                  <p>${new Date(loc.start_datetime).toLocaleString("en-NZ", {
                    dateStyle: "medium",
                    timeStyle: "short",
                  })}</p>
                  <div class="d-flex justify-content-center">
                    <a href="/event/${
                      loc.id
                    }/detail" class="btn btn-dark rounded-pill px-4 text-white">View</a>
                  </div>
                </div>
              `
                )
                .join("")}
            </div>
            ${
              locsAtSamePlace.length > 1
                ? `
              <button class="carousel-button next" onclick="moveSlide(this, 1)">&#10095;</button>
            `
                : ""
            }
          </div>
          <div class="carousel-indicator">
            <span class="current-slide">1</span>/<span class="total-slides">${
              locsAtSamePlace.length
            }</span>
          </div>
        </div>
      `;

      marker.bindPopup(popupContent);
    });

    // Fit map to show all markers if multiple locations
    if (pathCoordinates.length > 1) {
      const bounds = L.latLngBounds(pathCoordinates);
      window.fullJourneyMapInstance.fitBounds(bounds, { padding: [30, 30] });
    }

    // Invalidate size to ensure proper rendering
    setTimeout(() => window.fullJourneyMapInstance.invalidateSize(), 100);
  } catch (error) {
    console.error("Error initializing modal journey map:", error);
  }
}

function moveSlide(btn, direction) {
  const container = btn.closest(".location-popup");
  const slides = Array.from(container.querySelectorAll(".carousel-slide"));
  let currentIndex = slides.findIndex(
    (slide) => slide.style.display === "block"
  );

  slides[currentIndex].style.display = "none";
  const nextIndex = (currentIndex + direction + slides.length) % slides.length;
  slides[nextIndex].style.display = "block";

  // Update indicator
  const popup = btn.closest(".location-popup");
  const currentSlideEl = popup.querySelector(".current-slide");
  if (currentSlideEl) {
    currentSlideEl.textContent = nextIndex + 1;
  }
}

/**
 * Toggle event map visibility
 * @param {number} eventId - Event ID
 */
function toggleEventMap(eventId) {
  const mapContainer = document.getElementById(`inlineMap${eventId}`);
  const toggleButton = document.querySelector(
    `[data-event-id="${eventId}"][data-action="toggle-map"]`
  );

  if (!mapContainer || !toggleButton) {
    console.error(
      `Map container or toggle button not found for event ${eventId}`
    );
    return;
  }

  const isVisible = mapContainer.style.display !== "none";

  if (isVisible) {
    // Hide map
    mapContainer.style.display = "none";
    toggleButton.innerHTML = '<i class="bi bi-geo-alt"></i> Show Location';

    // Clean up map instance
    if (eventMaps[eventId]) {
      eventMaps[eventId].remove();
      delete eventMaps[eventId];
    }
  } else {
    // Show map
    mapContainer.style.display = "block";
    toggleButton.innerHTML = '<i class="bi bi-geo-alt-fill"></i> Hide Location';

    // Initialize map if not already done
    initializeEventMap(eventId);
  }
}

/**
 * Initialize an individual event map
 * @param {number} eventId - Event ID
 */
function initializeEventMap(eventId) {
  const mapContainer = document.getElementById(`inlineMap${eventId}`);
  if (!mapContainer) {
    console.error(`Map container not found for event ${eventId}`);
    return;
  }

  const latitude = mapContainer.dataset.latitude;
  const longitude = mapContainer.dataset.longitude;
  const locationName = mapContainer.dataset.locationName;

  if (!latitude || !longitude) {
    console.error(`Missing coordinates for event ${eventId}`);
    return;
  }

  try {
    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);

    if (isNaN(lat) || isNaN(lng)) {
      console.error(
        `Invalid coordinates for event ${eventId}: lat=${latitude}, lng=${longitude}`
      );
      return;
    }

    const map = L.map(`inlineMap${eventId}`).setView([lat, lng], 15);
    L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png", {
      maxZoom: 19,
      attribution:
        '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
    }).addTo(map);

    const redIcon = createRedIcon();
    L.marker([lat, lng], { icon: redIcon })
      .addTo(map)
      .bindPopup(
        `<div class="location-popup"><h6>${
          locationName || "Location"
        }</h6></div>`
      )
      .openPopup();

    eventMaps[eventId] = map;
  } catch (error) {
    console.error(`Error initializing event map ${eventId}:`, error);
  }
}

/* ===== EDIT HISTORY ===== */

/**
 * Open edit history modal for a journey
 * @param {number} journeyId - Journey ID
 * @param {string} journeyTitle - Journey title for display
 */
function openEditHistoryModal(journeyId, journeyTitle) {
  if (!journeyId) {
    console.error("Journey ID is required");
    return;
  }

  console.log(
    `Opening edit history modal for journey ${journeyId}: ${journeyTitle}`
  );

  // Check if showModal function is available
  if (typeof showModal !== "function") {
    console.error(
      "showModal function not available! Cannot open edit history modal."
    );
    return;
  }

  // Show loading state using common modal
  const loadingContent = `
    <div class="edit-history-loading">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p>Loading edit history...</p>
    </div>
  `;

  showModal(`Edit History`, loadingContent);

  // Fetch edit history modal content (server-side rendered template)
  fetch(`/edit-history/modal/journey/${journeyId}`)
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.text();
    })
    .then((html) => {
      // Update modal with the server-rendered content using common modal
      showModal(`Edit History`, html);
    })
    .catch((error) => {
      console.error("Error fetching edit history modal:", error);

      // Show error using common modal
      const errorContent = `
        <div class="edit-history-empty">
          <i class="bi bi-exclamation-triangle"></i>
          <h4>Error Loading History</h4>
          <p>Failed to load edit history. Please try again.</p>
        </div>
      `;

      showModal(`Edit History`, errorContent);
    });
}

/**
 * Open event location modal
 * @param {string|null} latitude - Latitude coordinate
 * @param {string|null} longitude - Longitude coordinate
 * @param {string} locationName - Location name
 * @param {string|null} locationId - Location ID for follow functionality
 */
function openEventLocationModal(latitude, longitude, locationName, locationId) {
  if (!latitude || !longitude) {
    console.warn("Invalid coordinates for event location modal");
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage("Location coordinates not available", "info");
    }
    return;
  }

  const modal = new bootstrap.Modal(
    document.getElementById("eventLocationModal")
  );
  document.getElementById("eventLocationModalLabel").textContent =
    locationName || "Event Location";

  // Store location ID for follow functionality
  const modalElement = document.getElementById("eventLocationModal");
  modalElement.dataset.locationId = locationId || "";

  // Update follow button if location ID is available
  if (locationId) {
    updateLocationFollowButton(locationId);
  }

  modal.show();

  // Initialize event location map after modal is shown
  setTimeout(() => {
    initializeFullEventMap(latitude, longitude, locationName);
  }, 300);
}

/**
 * Initialize full event map in modal
 * @param {string} latitude - Latitude coordinate
 * @param {string} longitude - Longitude coordinate
 * @param {string} locationName - Location name
 */
function initializeFullEventMap(latitude, longitude, locationName) {
  const mapElement = document.getElementById("fullEventMap");
  if (!mapElement || typeof L === "undefined") {
    console.warn("Full event map element not found or Leaflet not loaded");
    return;
  }

  try {
    // Remove existing map if any
    if (window.fullEventMapInstance) {
      window.fullEventMapInstance.remove();
    }

    const lat = parseFloat(latitude);
    const lng = parseFloat(longitude);

    if (isNaN(lat) || isNaN(lng)) {
      console.error(
        `Invalid coordinates for full event map: lat=${latitude}, lng=${longitude}`
      );
      return;
    }

    window.fullEventMapInstance = L.map("fullEventMap").setView([lat, lng], 15);
    L.tileLayer("https://tile.openstreetmap.org/{z}/{x}/{y}.png", {
      maxZoom: 19,
      attribution:
        '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
    }).addTo(window.fullEventMapInstance);

    const redIcon = createRedIcon();
    L.marker([lat, lng], { icon: redIcon })
      .addTo(window.fullEventMapInstance)
      .bindPopup(
        `<div class="location-popup"><h6>${
          locationName || "Location"
        }</h6></div>`
      )
      .openPopup();
  } catch (error) {
    console.error("Error initializing full event map:", error);
  }
}

/**
 * Update location follow button based on current follow status
 * @param {string} locationId - Location ID to check follow status for
 */
async function updateLocationFollowButton(locationId) {
  try {
    const response = await fetch(`/location/api/${locationId}/follow-status`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    if (response.ok) {
      const data = await response.json();
      const followButton = document.querySelector(
        '#eventLocationModal [data-action="toggle-location-follow"]'
      );

      if (followButton) {
        const icon = followButton.querySelector("i");
        const text = followButton.querySelector(".btn-text");

        if (data.is_following) {
          followButton.classList.remove("btn-outline-primary");
          followButton.classList.add("btn-primary");
          if (icon) {
            icon.className = "bi bi-heart-fill me-1";
          }
          if (text) {
            text.textContent = "Following";
          }
        } else {
          followButton.classList.remove("btn-primary");
          followButton.classList.add("btn-outline-primary");
          if (icon) {
            icon.className = "bi bi-heart me-1";
          }
          if (text) {
            text.textContent = "Follow";
          }
        }

        // Update the location ID data attribute
        followButton.dataset.locationId = locationId;
      }
    } else {
      console.error("Failed to get location follow status");

      // Show error message to user if the API call fails
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(
          "Unable to check follow status. Please try again.",
          "warning"
        );
      }
    }
  } catch (error) {
    console.error("Error updating location follow button:", error);

    // Show network error message to user
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage(
        "Network error while checking follow status.",
        "warning"
      );
    }
  }
}

/**
 * Toggle follow status for a location
 * @param {string} locationId - Location ID to toggle follow for
 * @param {HTMLElement} button - Follow button element
 */
async function toggleLocationFollow(locationId, button) {
  try {
    button.disabled = true;

    const response = await fetch(`/location/api/${locationId}/toggle-follow`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    const data = await response.json();

    if (data.success) {
      // Update button text and icon
      const icon = button.querySelector("i");
      const text = button.querySelector(".btn-text");

      if (data.is_following) {
        button.classList.remove("btn-outline-primary");
        button.classList.add("btn-primary");
        if (icon) {
          icon.className = "bi bi-heart-fill me-1";
        }
        if (text) {
          text.textContent = "Following";
        }
      } else {
        button.classList.remove("btn-primary");
        button.classList.add("btn-outline-primary");
        if (icon) {
          icon.className = "bi bi-heart me-1";
        }
        if (text) {
          text.textContent = "Follow";
        }
      }

      // Show success message to user
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(data.message, "success");
      }
    } else {
      console.error("Failed to toggle location follow:", data.message);

      // Show error message to user
      if (typeof window.showFlashMessage === "function") {
        window.showFlashMessage(data.message, "danger");
      }
    }
  } catch (error) {
    console.error("Error toggling location follow:", error);

    // Show network error message to user
    if (typeof window.showFlashMessage === "function") {
      window.showFlashMessage(
        "Network error. Please check your connection and try again.",
        "danger"
      );
    }
  } finally {
    button.disabled = false;
  }
}

/* ===== INITIALIZATION ===== */

// File integrity check
console.log(
  "Journey Detail Bundle file loaded completely - all functions available"
);
