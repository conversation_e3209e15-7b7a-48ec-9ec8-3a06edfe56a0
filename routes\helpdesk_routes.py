from flask import Blueprint, g, render_template, request, redirect, url_for, flash, session, jsonify
from datetime import datetime
from services import helpdesk_service as helpdesk_service
from services import subscription_service as subscription_service
from services import user_service as user_service
from services import notification_service as notification_service
from utils.security import login_required, content_manager_required

bp = Blueprint('helpdesk', __name__, url_prefix='/helpdesk')

@bp.route('/manage', methods=['GET'])
@content_manager_required
def get_tickets():

    page = request.args.get('page', 1, type=int)
    user_id=request.args.get('user_id')
    search = request.args.get('search', '')
    status_filter = request.args.get('status')
    type_filter = request.args.get('category')
    tab = request.args.get('tab', 'all')  # New tab parameter


    from utils.permissions import PermissionChecker
    if user_id is None and not PermissionChecker.can_manage_content():
        user_id=session['user_id']

    if user_id is not None and int(user_id)!=int(session['user_id']):
        flash('Authentication Error, redirect to personal help desk.', 'danger')
        user_id=session['user_id']

    limit = 10
    offset = (page - 1) * limit

    # Handle tab-based filtering for content managers
    if PermissionChecker.can_manage_content():
        if tab == 'assigned_to_me':
            tickets = helpdesk_service.get_assigned_tickets(
                staff_id=session['user_id'],
                limit=limit,
                offset=offset,
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
        elif tab == 'new':
            tickets = helpdesk_service.get_unassigned_tickets(
                limit=limit,
                offset=offset,
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
        elif tab == 'active':
            tickets = helpdesk_service.get_active_tickets(
                limit=limit,
                offset=offset,
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
        elif tab == 'past':
            tickets = helpdesk_service.get_past_tickets(
                limit=limit,
                offset=offset,
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
        else:  # tab == 'all'
            tickets = helpdesk_service.get_tickets(
                user_id=None,  # Get all tickets for staff
                limit=limit,
                offset=offset,
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
    else:
        # Regular users only see their own tickets
        tickets = helpdesk_service.get_tickets(
            user_id=user_id,
            limit=limit,
            offset=offset,
            search=search,
            status_filter=status_filter,
            type_filter=type_filter
        )

    for t in tickets:
        # Initialize flags to False
        t['is_member'] = False
        t['is_admin'] = False

        _,total = subscription_service.get_user_subscription_history(t['user_id'],1,0)
        h_list,_=subscription_service.get_user_subscription_history(t['user_id'],total,0)

        if isinstance(t['created_at'], str):
            ticketCreateDate = datetime.strptime(t['created_at'], '%Y-%m-%d %H:%M:%S').date()  # 根据实际格式调整
        elif isinstance(t['created_at'], datetime):
            ticketCreateDate = t['created_at'].date()
        else:
            ticketCreateDate = datetime.fromtimestamp(t['created_at']).date()

        for h in h_list:
            if h['start_date'] <= ticketCreateDate <= h['end_date']:
                t['is_member'] = True

                if t['user_id']==5:
                    print("h['end_date']=",h['end_date'])
                break

        user = user_service.get_user_by_id(t['user_id'])

        from utils.permissions import PermissionGroups
        if user is not None and user['role'] in PermissionGroups.STAFF:
            t['is_admin']=True

    # Sort tickets by tag column priority: Priority > Staff > Empty, then by created_at DESC
    def get_tag_priority(ticket):
        if ticket.get('is_member'):  # Priority tag
            return 0
        elif ticket.get('is_admin'):  # Staff tag
            return 1
        else:  # Empty tag
            return 2

    def get_created_timestamp(ticket):
        created_at = ticket.get('created_at')
        if isinstance(created_at, str):
            return datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S').timestamp()
        elif isinstance(created_at, datetime):
            return created_at.timestamp()
        else:
            return created_at if created_at else 0

    # Sort by tag priority first, then by created_at descending (newest first)
    tickets = sorted(tickets, key=lambda t: (get_tag_priority(t), -get_created_timestamp(t)))



    # Calculate total count based on tab
    if PermissionChecker.can_manage_content():
        if tab == 'assigned_to_me':
            total_count = helpdesk_service.get_assigned_tickets_count(
                staff_id=session['user_id'],
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
        elif tab == 'new':
            total_count = helpdesk_service.get_unassigned_tickets_count(
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
        elif tab == 'active':
            total_count = helpdesk_service.get_active_tickets_count(
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
        elif tab == 'past':
            total_count = helpdesk_service.get_past_tickets_count(
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
        else:  # tab == 'all'
            total_count = helpdesk_service.get_tickets_count(
                user_id=None,
                search=search,
                status_filter=status_filter,
                type_filter=type_filter
            )
    else:
        total_count = helpdesk_service.get_tickets_count(
            user_id=user_id,
            search=search,
            status_filter=status_filter,
            type_filter=type_filter
        )

    total_pages = (total_count + limit - 1) // limit

    return render_template(
        'helpdesk/list.html',
        tickets=tickets,
        page=page,
        total_pages=total_pages,
        total_count=total_count,
        status_filter=status_filter,
        type_filter=type_filter,
        search_term=search,
        current_tab=tab
    )

@bp.route('', methods=['GET'])
@login_required
def get_user_tickets():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status')
    type_filter = request.args.get('category')
    limit = 10
    offset = (page - 1) * limit

    user_id = session['user_id']
    tickets = helpdesk_service.get_tickets(
        user_id=user_id,
        limit=limit,
        offset=offset,
        search=search,
        status_filter=status_filter,
        type_filter=type_filter
    )
    total_count = helpdesk_service.get_tickets_count(
        user_id=user_id,
        search=search,
        status_filter=status_filter,
        type_filter=type_filter
    )
    total_pages = (total_count + limit - 1) // limit

    print(f"Category filter received: {type_filter}")

    return render_template(
        'helpdesk/list.html',
        tickets=tickets,
        page=page,
        total_pages=total_pages,
        total_count=total_count,
        status_filter=status_filter,
        type_filter=type_filter,
        search_term=search
    )

@bp.route('/ticket/new', methods=['GET'])
@login_required
def get_ticket_form():
    """Get the form for creating a new ticket"""

    return render_template('helpdesk/create.html')

@bp.route('/appeal/ban/<username>', methods=['GET', 'POST'])
def ban_appeal_form(username):
    """Show ban appeal status and form for a specific username"""
    # Get user by username
    from data import user_data, helpdesk_data
    user = user_data.get_user_by_username(username)

    if not user:
        flash('Username not found', 'danger')
        return redirect(url_for('main.get_landing_page'))

    if not user.get('is_banned', False):
        flash('This account is not currently banned', 'info')
        return redirect(url_for('main.get_landing_page'))

    if request.method == 'GET':
        # Check for existing appeal status
        appeal_status = helpdesk_data.get_banned_user_appeal_status(user['id'])

        # If user has an approved appeal but is still banned, something is wrong
        if appeal_status and appeal_status['status'] == 'approved':
            # This shouldn't happen - if appeal was approved, user should be unbanned
            # Show only the status, no new appeal form
            pass

        return render_template('helpdesk/banned_user_appeal.html',
                             user=user,
                             appeal_status=appeal_status)

    else:  # POST
        reason = request.form.get('reason', '').strip()

        if not reason:
            flash('Please provide a reason for your appeal', 'danger')
            return redirect(url_for('helpdesk.ban_appeal_form', username=username))

        success, message, appeal_id = helpdesk_service.create_banned_user_appeal(
            user_id=user['id'],
            reason=reason
        )

        flash(message, 'success' if success else 'danger')
        return redirect(url_for('helpdesk.ban_appeal_form', username=username))

@bp.route('/create', methods=['GET','POST'])
def create_ticket():
    user_id = session.get('user_id')
    if not user_id or user_id == -1:
        user_id = None
    title = request.form.get('title')
    category = request.form.get('category')
    description = request.form.get('description')
    username = request.form.get('username')  # For ban appeals

    success, message, ticket_id = helpdesk_service.add_ticket(user_id, title, category, description, username)

    flash(message, 'success' if success else 'danger')
    if 'manage' in request.referrer:
        return redirect(url_for('helpdesk.get_tickets', page=1))  
    elif user_id:
        return redirect(url_for('helpdesk.get_user_tickets', page=1, user_id=user_id))  
    else:
        return redirect(url_for('main.get_landing_page'))

@bp.route('/query', methods=['GET'])
@login_required
def query_ticket():

    ticket_id=request.args.get('ticket_id')
    success,message,ticket=helpdesk_service.query_ticket_by_id(ticket_id)

    staff_list=helpdesk_service.query_staff()

    # flash(message, 'success' if success else 'danger')
    return render_template('helpdesk/detail.html',ticket=ticket,staff_list=staff_list,status=ticket['status'])

@bp.route('/reply/add', methods=['GET','POST'])
@login_required
def add_reply():
    user_id = request.form.get('user_id')
    ticket_id = request.form.get('ticket_id')
    content = request.form.get('content')

    # Get ticket info for permission validation
    success, message, ticket = helpdesk_service.query_ticket_by_id(ticket_id)
    if not success or not ticket:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Ticket not found'})
        flash('Ticket not found', 'danger')
        return redirect(url_for('helpdesk.get_user_tickets'))

    # Validate user permission to comment
    current_user_id = int(session['user_id'])
    ticket_creator_id = int(ticket['user_id']) if ticket['user_id'] else None
    assigned_staff_id = int(ticket['assigned_to']) if ticket['assigned_to'] else None

    # Helper function to create redirect with preserved back parameter
    def create_ticket_redirect():
        back_url = request.form.get('back')
        redirect_url = url_for('helpdesk.query_ticket', ticket_id=ticket_id)
        if back_url:
            redirect_url += f'?back={back_url}'
        return redirect(redirect_url)

    # Check if user is either the ticket creator or assigned staff
    if current_user_id != ticket_creator_id and current_user_id != assigned_staff_id:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'You do not have permission to comment on this ticket'})
        flash('You do not have permission to comment on this ticket', 'danger')
        return create_ticket_redirect()

    # Check if ticket status allows comments
    if ticket['status'] in ['resolved', 'approved', 'rejected']:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'This ticket is closed and no longer accepts comments'})
        flash('This ticket is closed and no longer accepts comments', 'danger')
        return create_ticket_redirect()

    # Validate that the user_id from form matches session (security check)
    if int(user_id) != current_user_id:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Authentication error'})
        flash('Authentication error', 'danger')
        return create_ticket_redirect()

    success, message, reply_id = helpdesk_service.add_reply_to_ticket(user_id, ticket_id, content)
    if success != True:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': message})
        flash(message, 'danger')
        return create_ticket_redirect()

    # Create notification for the other party (if commenter is not the recipient)
    if int(user_id) != ticket_creator_id:
        # Staff member commented, notify ticket creator (only if not a guest ticket)
        if ticket['user_id']:
            commenter = user_service.get_user_by_id(user_id)
            if commenter:
                notification_content = f"{commenter['username']} replied to your ticket: {ticket['subject']}"
                notification_service.create_notification(
                    user_id=ticket['user_id'],
                    notification_type='helpdesk',
                    content=notification_content,
                    related_id=ticket['id']
                )
    elif assigned_staff_id:
        # Ticket creator commented, notify assigned staff
        notification_content = f"New reply on ticket: {ticket['subject']}"
        notification_service.create_notification(
            user_id=assigned_staff_id,
            notification_type='helpdesk',
            content=notification_content,
            related_id=ticket['id']
        )

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # Get user info for the response
        user = user_service.get_user_by_id(user_id)
        return jsonify({
            'success': True,
            'message': 'Comment added successfully',
            'reply': {
                'id': reply_id,
                'content': content,
                'username': user['username'],
                'profile_image': user.get('profile_image'),
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        })

    flash(message, 'success')
    return create_ticket_redirect()

@bp.route('/ticket/assign', methods=['GET'])
@login_required
def assign_ticket_form():

    ticket_id=request.args.get('ticket_id')

    return render_template('helpdesk/assign.html',ticket_id=ticket_id)

@bp.route('/assign', methods=['GET','POST'])
@content_manager_required
def assign_ticket():
    """Allow content managers to assign tickets to other staff members"""
    from utils.permissions import PermissionChecker

    if not PermissionChecker.can_manage_content():
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'You do not have permission to assign tickets'})
        flash('You do not have permission to assign tickets', 'danger')
        return redirect(url_for('helpdesk.get_tickets'))

    ticket_id = request.form.get('ticket_id')
    staff_id = request.form.get('staff_id')
    user_id = g.current_user['id']

    # Validate required parameters
    if not ticket_id:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Ticket ID is required'})
        flash('Ticket ID is required', 'danger')
        return redirect(url_for('helpdesk.get_tickets'))

    if not staff_id:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Please select a staff member to assign the ticket to'})
        flash('Please select a staff member to assign the ticket to', 'danger')
        # Preserve back parameter when redirecting after failed assignment
        back_url = request.form.get('back')
        redirect_url = url_for('helpdesk.query_ticket', ticket_id=ticket_id)
        if back_url:
            redirect_url += f'?back={back_url}'
        return redirect(redirect_url)

    # Get ticket info first for validation and notifications
    success_ticket, _, ticket = helpdesk_service.query_ticket_by_id(ticket_id)
    if not success_ticket or not ticket:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Ticket not found'})
        flash('Ticket not found', 'danger')
        return redirect(url_for('helpdesk.get_tickets'))

    # Check if staff member exists and is valid
    assignee = user_service.get_user_by_id(staff_id)
    if not assignee:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Selected staff member not found'})
        flash('Selected staff member not found', 'danger')
        # Preserve back parameter when redirecting after failed assignment
        back_url = request.form.get('back')
        redirect_url = url_for('helpdesk.query_ticket', ticket_id=ticket_id)
        if back_url:
            redirect_url += f'?back={back_url}'
        return redirect(redirect_url)

    # Perform assignment
    success, message, row_affected = helpdesk_service.assign_ticket(ticket_id, staff_id, user_id)
    
    if success:
        # Create notifications on successful assignment
        # Only notify ticket creator if it's not a guest ticket
        if ticket['user_id']:
            notification_to_user = f"Your ticket '{ticket['subject']}' has been assigned to {assignee['username']}."
            notification_service.create_notification(
                user_id=ticket['user_id'],
                notification_type='helpdesk',
                content=notification_to_user,
                related_id=ticket['id']
            )

        notification_to_assignee = f"Ticket '{ticket['subject']}' has been assigned to you."
        notification_service.create_notification(
            user_id=assignee['id'],
            notification_type='helpdesk',
            content=notification_to_assignee,
            related_id=ticket['id']
        )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # Get updated ticket info for response
            _, _, updated_ticket = helpdesk_service.query_ticket_by_id(ticket_id)
            # Get updated staff list for assignment form
            staff_list = helpdesk_service.query_staff()
            return jsonify({
                'success': True,
                'message': f"Ticket successfully assigned to {assignee['username']}",
                'assigned_badge_html': render_template('helpdesk/components/assigned_badge.html',
                                                    ticket=updated_ticket,
                                                    session=session),
                'actions_html': render_template('helpdesk/components/ticket_actions.html',
                                             ticket=updated_ticket,
                                             session=session),
                'assign_form_html': render_template('helpdesk/components/assign_form.html',
                                                 ticket=updated_ticket,
                                                 staff_list=staff_list,
                                                 session=session),
                'take_form_html': render_template('helpdesk/components/take_form.html',
                                               ticket=updated_ticket,
                                               session=session),
                'updated_ticket': updated_ticket
            })

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': False, 'message': message})

    flash(message, 'success' if success else 'danger')

    # Preserve back parameter when redirecting after assignment
    back_url = request.form.get('back')
    redirect_url = url_for('helpdesk.query_ticket', ticket_id=ticket_id)
    if back_url:
        redirect_url += f'?back={back_url}'

    return redirect(redirect_url)

@bp.route('/status/change', methods=['GET', 'POST'])
@content_manager_required
def change_ticket_status():
    """Change the status of a ticket"""
    from utils.permissions import PermissionChecker

    if not PermissionChecker.can_manage_content():
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'You do not have permission to change ticket status'})
        flash('You do not have permission to change ticket status', 'danger')
        return redirect(url_for('helpdesk.get_tickets'))

    # Handle both GET (legacy) and POST (new form-based) requests
    if request.method == 'POST':
        ticket_id = request.form.get('ticket_id')
        status = request.form.get('status')
    else:
        ticket_id = request.args.get('ticket_id')
        status = request.args.get('status')

    if not ticket_id or not status:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'Missing ticket ID or status'})
        flash('Missing ticket ID or status', 'danger')
        return redirect(url_for('helpdesk.get_tickets'))

    success, message, ticket = helpdesk_service.query_ticket_by_id(ticket_id)

    if not success:
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': message})
        flash(message, 'danger')
        return redirect(url_for('helpdesk.get_tickets'))

    success, message, row_affected = helpdesk_service.change_ticket_status(ticket_id, status)

    if success:
        # Create notification for the ticket submitter (only if not a guest ticket)
        if ticket['user_id']:
            notification_to_user = f"The status of your ticket '{ticket['subject']}' has been changed to {status}."
            notification_service.create_notification(
                user_id=ticket['user_id'],
                notification_type='helpdesk',
                content=notification_to_user,
                related_id=ticket['id']
            )

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # Get updated ticket info for response
            _, _, updated_ticket = helpdesk_service.query_ticket_by_id(ticket_id)
            status_text = {
                'new': 'New',
                'open': 'Open',
                'stalled': 'Stalled',
                'resolved': 'Resolved',
                'approved': 'Approved',
                'rejected': 'Rejected'
            }.get(status, status.title())
            return jsonify({
                'success': True,
                'message': f"Ticket status changed to {status} successfully",
                'new_status': status,
                'status_text': status_text,
                'actions_html': render_template('helpdesk/components/ticket_actions.html',
                                             ticket=updated_ticket,
                                             session=session)
            })

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': False, 'message': message})

    flash(message, 'success' if success else 'danger')

    # For POST requests, redirect to avoid form resubmission
    if request.method == 'POST':
        # Preserve back parameter when redirecting after status change
        back_url = request.form.get('back')
        redirect_url = url_for('helpdesk.query_ticket', ticket_id=ticket_id)
        if back_url:
            redirect_url += f'?back={back_url}'
        return redirect(redirect_url)
    else:
        # For GET requests, render template directly (legacy behavior)
        staff_list = helpdesk_service.query_staff()
        return render_template('helpdesk/detail.html', ticket=ticket, staff_list=staff_list, status=ticket['status'])

@bp.route('/abandon', methods=['GET'])
@login_required
def abandon_ticket():
    ticket_id=request.args.get('ticket_id')

    staff_list=helpdesk_service.query_staff()
    success,message,ticket=helpdesk_service.query_ticket_by_id(ticket_id)

    success,message,row_affected=helpdesk_service.abandon_ticket(ticket_id)

    flash(message, 'success' if success else 'danger')
    return render_template('helpdesk/detail.html',ticket=ticket,staff_list=staff_list,status=ticket['status'])

@bp.route('/take', methods=['POST'])
@content_manager_required
def take_ticket():
    """Allow content managers to take a ticket (including taking over from others)"""
    from utils.permissions import PermissionChecker

    if not PermissionChecker.can_manage_content():
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'You do not have permission to take tickets'})
        flash('You do not have permission to take tickets', 'danger')
        return redirect(url_for('helpdesk.get_tickets'))

    ticket_id = request.form.get('ticket_id')
    staff_id = g.current_user['id']

    success, message, result = helpdesk_service.take_ticket(ticket_id, staff_id)

    if success:
        # Get ticket and assignee info for notifications
        success_ticket, _, ticket = helpdesk_service.query_ticket_by_id(ticket_id)
        if success_ticket and ticket:
            assignee = user_service.get_user_by_id(staff_id)
            if assignee:
                # Notification to ticket submitter (only if not a guest ticket)
                if ticket['user_id']:
                    if result.get('previous_assignee'):
                        # This was a takeover
                        notification_content = f"Your ticket '{ticket['subject']}' has been taken over by {assignee['username']}."
                    else:
                        # This was a normal take
                        notification_content = f"Your ticket '{ticket['subject']}' has been taken by {assignee['username']}."

                    notification_service.create_notification(
                        user_id=ticket['user_id'],
                        notification_type='helpdesk',
                        content=notification_content,
                        related_id=ticket['id']
                    )

                # If this was a takeover, notify the previous assignee
                if result.get('previous_assignee'):
                    previous_assignee = user_service.get_user_by_id(result['previous_assignee'])
                    if previous_assignee:
                        takeover_notification = f"Ticket '{ticket['subject']}' has been taken over by {assignee['username']}."
                        notification_service.create_notification(
                            user_id=result['previous_assignee'],
                            notification_type='helpdesk',
                            content=takeover_notification,
                            related_id=ticket['id']
                        )

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        if success:
            # Get updated ticket info for response
            _, _, updated_ticket = helpdesk_service.query_ticket_by_id(ticket_id)
            if updated_ticket:
                # Get updated staff list for assignment form
                staff_list = helpdesk_service.query_staff()
                return jsonify({
                    'success': True,
                    'message': message,
                    'assigned_badge_html': render_template('helpdesk/components/assigned_badge.html',
                                                        ticket=updated_ticket,
                                                        session=session),
                    'actions_html': render_template('helpdesk/components/ticket_actions.html',
                                                 ticket=updated_ticket,
                                                 session=session),
                    'assign_form_html': render_template('helpdesk/components/assign_form.html',
                                                     ticket=updated_ticket,
                                                     staff_list=staff_list,
                                                     session=session),
                    'take_form_html': render_template('helpdesk/components/take_form.html',
                                                   ticket=updated_ticket,
                                                   session=session),
                    'updated_ticket': updated_ticket
                })
            else:
                return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'message': message})



    flash(message, 'success' if success else 'danger')

    # Preserve back parameter when redirecting after taking ticket
    back_url = request.form.get('back')
    redirect_url = url_for('helpdesk.query_ticket', ticket_id=ticket_id)
    if back_url:
        redirect_url += f'?back={back_url}'

    return redirect(redirect_url)

@bp.route('/drop', methods=['POST'])
@content_manager_required
def drop_ticket():
    """Allow content managers to drop an assigned ticket back to the queue"""
    from utils.permissions import PermissionChecker

    if not PermissionChecker.can_manage_content():
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'message': 'You do not have permission to drop tickets'})
        flash('You do not have permission to drop tickets', 'danger')
        return redirect(url_for('helpdesk.get_tickets'))

    ticket_id = request.form.get('ticket_id')

    success, message, _ = helpdesk_service.drop_ticket(ticket_id)

    if success:
        # Create notification for the ticket submitter
        success_ticket, _, ticket = helpdesk_service.query_ticket_by_id(ticket_id)
        if success_ticket and ticket and ticket['user_id']:
            notification_content = f"Your ticket '{ticket['subject']}' has been returned to the queue for reassignment."
            notification_service.create_notification(
                user_id=ticket['user_id'],
                notification_type='helpdesk',
                content=notification_content,
                related_id=ticket['id']
            )

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        if success:
            # Get updated ticket info for response
            _, _, updated_ticket = helpdesk_service.query_ticket_by_id(ticket_id)
            if updated_ticket:
                # Get updated staff list for assignment form
                staff_list = helpdesk_service.query_staff()
                return jsonify({
                    'success': True,
                    'message': message,
                    'assigned_badge_html': render_template('helpdesk/components/assigned_badge.html',
                                                        ticket=updated_ticket,
                                                        session=session),
                    'actions_html': render_template('helpdesk/components/ticket_actions.html',
                                                 ticket=updated_ticket,
                                                 session=session),
                    'assign_form_html': render_template('helpdesk/components/assign_form.html',
                                                     ticket=updated_ticket,
                                                     staff_list=staff_list,
                                                     session=session),
                    'take_form_html': render_template('helpdesk/components/take_form.html',
                                                   ticket=updated_ticket,
                                                   session=session),
                    'updated_ticket': updated_ticket
                })
            else:
                return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'message': message})



    flash(message, 'success' if success else 'danger')

    # Preserve back parameter when redirecting after dropping ticket
    back_url = request.form.get('back')
    redirect_url = url_for('helpdesk.query_ticket', ticket_id=ticket_id)
    if back_url:
        redirect_url += f'?back={back_url}'

    return redirect(redirect_url)

@bp.route('/api/chatbot', methods=['POST'])
def chatbot_message():
    data = request.json
    message = data.get('message', '').strip()

    if not message:
        return jsonify({'success': False, 'error': 'Message is empty'}), 400

    # 使用 -1 表示匿名用户（guest）
    user_id = session.get('user_id') or -1

    success, msg, ticket_id = helpdesk_service.add_ticket(
        user_id=user_id,
        subject='ChatBot Help',
        category='ChatBot',
        description=message,
        username=None  # Chatbot doesn't handle ban appeals
    )

    if success:
        return jsonify({'success': True, 'response': 'Your message has been submitted to Helpdesk.'})
    else:
        return jsonify({'success': False, 'error': msg}), 500

# ===== Journey Appeal Routes =====

@bp.route('/appeal/journey/<int:journey_id>', methods=['GET', 'POST'])
@login_required
def journey_appeal(journey_id):
    """Create or view appeal for a hidden journey"""
    user_id = session['user_id']

    if request.method == 'GET':
        # Check if journey exists and is hidden
        from data import journey_data
        journey = journey_data.get_journey(journey_id)

        if not journey:
            flash('Journey not found', 'danger')
            return redirect(url_for('journey.get_private_journeys'))

        if journey['user_id'] != user_id:
            flash('You can only appeal your own journeys', 'danger')
            return redirect(url_for('journey.get_private_journeys'))

        if not journey.get('is_hidden', False):
            flash('This journey is not hidden', 'info')
            return redirect(url_for('journey.get_journey', journey_id=journey_id))

        # Check for existing appeal
        appeal_status = helpdesk_service.get_journey_appeal_status(user_id, journey_id)

        return render_template('helpdesk/journey_appeal.html',
                             journey=journey,
                             appeal_status=appeal_status)

    else:  # POST
        reason = request.form.get('reason', '').strip()

        if not reason:
            flash('Please provide a reason for your appeal', 'danger')
            return redirect(url_for('helpdesk.journey_appeal', journey_id=journey_id))

        success, message, appeal_id = helpdesk_service.create_journey_appeal(
            user_id=user_id,
            journey_id=journey_id,
            reason=reason
        )

        flash(message, 'success' if success else 'danger')

        if success:
            return redirect(url_for('journey.get_private_journey', journey_id=journey_id))
        else:
            return redirect(url_for('helpdesk.journey_appeal', journey_id=journey_id))

@bp.route('/appeal/blocked-user', methods=['GET', 'POST'])
@login_required
def blocked_user_appeal():
    """Create or view appeal for blocked user status"""
    user_id = session['user_id']

    if request.method == 'GET':
        # Check if user is blocked
        from data import user_data
        user = user_data.get_user_by_id(user_id)

        if not user:
            flash('User not found', 'danger')
            return redirect(url_for('main.get_user_dashboard'))

        if not user.get('is_blocked', False):
            flash('Your account is not blocked from sharing', 'info')
            return redirect(url_for('main.get_user_dashboard'))

        # Check for existing appeal
        from data import helpdesk_data
        appeal_status = helpdesk_data.get_blocked_user_appeal_status(user_id)

        return render_template('helpdesk/blocked_user_appeal.html',
                             user=user,
                             appeal_status=appeal_status)

    else:  # POST
        reason = request.form.get('reason', '').strip()

        if not reason:
            flash('Please provide a reason for your appeal', 'danger')
            return redirect(url_for('helpdesk.blocked_user_appeal'))

        success, message, appeal_id = helpdesk_service.create_blocked_user_appeal(
            user_id=user_id,
            reason=reason
        )

        flash(message, 'success' if success else 'danger')

        if success:
            return redirect(url_for('main.get_user_dashboard'))
        else:
            return redirect(url_for('helpdesk.blocked_user_appeal'))


@bp.route('/appeal/<int:appeal_id>/process', methods=['POST'])
@content_manager_required
def process_appeal(appeal_id):
    """Process an appeal (approve/reject)"""
    action = request.form.get('action')
    response = request.form.get('response', '').strip()
    staff_id = session['user_id']

    if action not in ['approve', 'reject']:
        flash('Invalid action', 'danger')
        return redirect(url_for('helpdesk.query_ticket', ticket_id=appeal_id))

    # Get appeal to determine type
    from data import helpdesk_data
    appeal = helpdesk_data.get_appeal_request(appeal_id)

    if not appeal:
        flash('Appeal not found', 'danger')
        return redirect(url_for('helpdesk.get_tickets'))

    # Process based on appeal type
    if appeal['appeal_type'] == 'hidden_journey':
        success, message = helpdesk_service.process_journey_appeal(
            appeal_id=appeal_id,
            staff_id=staff_id,
            action=action,
            response=response
        )
    elif appeal['appeal_type'] == 'sharing_block':
        success, message = helpdesk_service.process_blocked_user_appeal(
            appeal_id=appeal_id,
            staff_id=staff_id,
            action=action,
            response=response
        )
    elif appeal['appeal_type'] == 'ban':
        success, message = helpdesk_service.process_ban_appeal(
            appeal_id=appeal_id,
            staff_id=staff_id,
            action=action,
            response=response
        )
    else:
        flash('Unknown appeal type', 'danger')
        # Preserve back parameter for error redirects
        # Check form data first, then fall back to request args
        back_url = request.form.get('back') or request.args.get('back')
        redirect_url = url_for('helpdesk.query_ticket', ticket_id=appeal_id)
        if back_url:
            redirect_url += f'?back={back_url}'
        return redirect(redirect_url)

    flash(message, 'success' if success else 'danger')

    # Preserve back parameter when redirecting after processing
    # Check form data first, then fall back to request args
    back_url = request.form.get('back') or request.args.get('back')
    redirect_url = url_for('helpdesk.query_ticket', ticket_id=appeal_id)
    if back_url:
        redirect_url += f'?back={back_url}'

    return redirect(redirect_url)




